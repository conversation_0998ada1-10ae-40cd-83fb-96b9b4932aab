plugins {
    id 'com.android.library'
    id 'kotlin-android'
    id 'kotlin-kapt'
}
apply from: "$rootDir/gradle/common_library.gradle"

android {
    defaultConfig {
//        resourcePrefix 'task_'
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles "consumer-rules.pro"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    namespace 'com.kanzhun.marry.module_new_task'
}

dependencies {
    implementation fileTree(dir: "libs", include: ["*.jar", "*.aar"])
    testImplementation 'junit:junit:4.+'
    androidTestImplementation 'androidx.test.ext:junit:1.1.2'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.3.0'
    kapt deps.wmrouter_compiler
    api project(":module_me")
    implementation project(path: ':lib_imageviewer')
    implementation deps.zp_sdk_support
}