#!/usr/bin/env bash
# 用于压缩git本地未提交的图片

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# 从脚本所在目录开始向上查找git根目录
find_git_root() {
    local current_dir="$1"
    while [ "$current_dir" != "/" ]; do
        if [ -d "$current_dir/.git" ]; then
            echo "$current_dir"
            return 0
        fi
        current_dir="$(dirname "$current_dir")"
    done
    return 1
}

# 查找git根目录
GIT_ROOT=$(find_git_root "$SCRIPT_DIR")

if [ -z "$GIT_ROOT" ]; then
    echo "错误：未找到git仓库根目录"
    exit 1
fi

echo "项目根目录：$GIT_ROOT"
cd "$GIT_ROOT" || exit 1

# 获取当前git的提交文件
echo "获取未提交的文件列表："
for file in $( exec git diff-index --name-only HEAD )
  do
    echo "$file"
done