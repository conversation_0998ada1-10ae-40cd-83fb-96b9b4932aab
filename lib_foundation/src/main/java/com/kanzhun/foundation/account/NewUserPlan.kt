package com.kanzhun.foundation.account

import android.app.Activity
import android.view.View
import androidx.lifecycle.MutableLiveData
import androidx.navigation.NavController
import androidx.navigation.NavOptions
import androidx.navigation.findNavController
import com.kanzhun.foundation.R
import com.kanzhun.foundation.utils.LogoutUtil


val planAB = MutableLiveData<String>("A")

fun setPlan(plan: String){
//    planAB.postValue(plan)
}

var planPosition: Int = 0
var initPosition: Int = 0

private val planAFragmentList = listOf(
    R.id.nickNameFragment,
    R.id.sexFragment,
    R.id.birthdayFragment,
    R.id.areaFragment,
    R.id.schoolFragment,
    R.id.loveTargetFragment,
    R.id.heightFragment,
    R.id.hukouFragment,
    R.id.hometownFragment,
    R.id.industryFragment,
    R.id.jobFragment,
    R.id.companyFragment,
    R.id.assetFragment,
    R.id.keywordFragment,
    R.id.interestFragment,
    R.id.introductionFragment,
    R.id.storyFragment,
)

private val planBFragmentList = listOf(
    R.id.nickNameFragment,
    R.id.sexFragment,
    R.id.birthdayFragment,
    R.id.areaFragment,
    R.id.schoolFragment,
    R.id.loveTargetFragment,
    R.id.keywordFragment,
    R.id.heightFragment,
    R.id.hukouFragment,
    R.id.hometownFragment,
    R.id.industryFragment,
    R.id.jobFragment,
    R.id.companyFragment,
    R.id.assetFragment,
    R.id.interestFragment,
    R.id.storyFragment,
    R.id.introductionFragment,
)

private val planCFragmentList = listOf(
    R.id.nickNameFragment,
    R.id.sexFragment,
    R.id.birthdayFragment,
    R.id.areaFragment,
    R.id.schoolFragment,
    R.id.loveTargetFragment,
    R.id.keywordFragment,
)

fun getPlanFragmentList():List<Int>{
    if (isPlanA()){
        return planAFragmentList
    }else if(isPlanB()){
        return planBFragmentList
    }
    return planCFragmentList
}

fun isPlanA(): Boolean {
    return planAB.value == "A"
}

fun isPlanB(): Boolean {
    return planAB.value == "B"
}

fun isPlanC(): Boolean {
    return planAB.value == "C"
}

fun getDefaultNavOptions(): NavOptions {
    return NavOptions.Builder()
        .setEnterAnim(R.anim.nav_enter)
        .setExitAnim(R.anim.nav_exit)
        .setPopEnterAnim(R.anim.nav_pop_enter)
        .setPopExitAnim(R.anim.nav_pop_exit)
        .build()
}

// 新增：专门用于后退操作的NavOptions，使用后退动画
fun getBackNavOptions(): NavOptions {
    return NavOptions.Builder()
        .setEnterAnim(R.anim.nav_pop_enter)    // 使用后退进入动画
        .setExitAnim(R.anim.nav_pop_exit)      // 使用后退退出动画
        .setPopEnterAnim(R.anim.nav_pop_enter)
        .setPopExitAnim(R.anim.nav_pop_exit)
        .build()
}

class NewUserPlan{

    private fun getNavController(view:View?): NavController? {
        return try {
            view?.findNavController()
        } catch (e: Exception) {
            null
        }
    }

    fun navigateBackOrFinish(activity: Activity?,view: View?) {
        val navController = getNavController(view)
        if (navController != null) {
            val currentDestinationId = navController.currentDestination?.id
            if (currentDestinationId != null && canNavigateBack(currentDestinationId)) {
                val previousFragmentId = getPreviousFragmentId(currentDestinationId)
                if (previousFragmentId != null) {
                    // 在后退时清理当前Fragment，避免累积
                    val backNavOptions = NavOptions.Builder()
                        .setEnterAnim(R.anim.nav_pop_enter)
                        .setExitAnim(R.anim.nav_pop_exit)
                        .setPopEnterAnim(R.anim.nav_pop_enter)
                        .setPopExitAnim(R.anim.nav_pop_exit)
                        .setPopUpTo(currentDestinationId, true) // 清理当前Fragment
                        .build()

                    navController.navigate(previousFragmentId, null, backNavOptions)
                    planPosition--
                } else {
                    if (!navController.popBackStack()) {
                        LogoutUtil.logoutDialog(activity)
                    }
                }
            } else {
                LogoutUtil.logoutDialog(activity)
            }
        } else {
            LogoutUtil.logoutDialog(activity)
        }
    }

    // 新增：获取当前步骤的前一个fragment ID，用于后退逻辑 只针对ab实验
    fun getPreviousFragmentId(currentFragmentId: Int): Int? {
        if (isPlanA() || isPlanB()){
            val index = getPlanFragmentList().indexOf(currentFragmentId)
//            if(index == initPosition){
//                return null
//            }
            if (currentFragmentId == 0){
                return null
            }else{
                return getPlanFragmentList()[index -1]
            }
        }
        return null
    }

    // 新增：检查是否可以后退
    fun canNavigateBack(currentFragmentId: Int): Boolean {
        return getPreviousFragmentId(currentFragmentId) != null
    }

    fun navigate(view: View?) {
        val navController = getNavController(view)
        if (navController != null && (isPlanA() || isPlanB())) {
            val fragmentList = getPlanFragmentList()

            // 检查是否已经到达最后一个页面
            if (planPosition + 1 < fragmentList.size) {
                val currentDestinationId = navController.currentDestination?.id
                val nextFragmentId = fragmentList[++planPosition]

                // 在前进时也清理当前Fragment，避免累积
                val forwardNavOptions = NavOptions.Builder()
                    .setEnterAnim(R.anim.nav_enter)
                    .setExitAnim(R.anim.nav_exit)
                    .setPopEnterAnim(R.anim.nav_pop_enter)
                    .setPopExitAnim(R.anim.nav_pop_exit)
                    .setPopUpTo(currentDestinationId ?: -1, true) // 清理当前Fragment
                    .build()

                navController.navigate(nextFragmentId, null, forwardNavOptions)
            }
        }
    }
}