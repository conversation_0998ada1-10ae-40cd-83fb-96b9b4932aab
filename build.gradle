// Top-level build file where you can add configuration options common to all sub-projects/modules.
buildscript {
    apply from: 'versions.gradle'
    addPluginRepos(repositories)
    dependencies {
        classpath libs.android.gradlePlugin
        classpath libs.kotlin.gradlePlugin
        // Add Kotlin Serialization plugin
        classpath "org.jetbrains.kotlin:kotlin-serialization:${versions.kotlin}"

        // NOTE: Do not place your application dependencies here; they belong
        // in the individual module build.gradle files
        // 增加agcp插件配置，推荐您使用最新版本的agcp插件。
        classpath 'com.huawei.agconnect:agcp:1.6.0.300'
        // 添加WMRouter插件
        classpath(deps.wmrouter_gradle_plugin) {
            exclude group: 'com.android.tools.build'
        }
//        classpath "com.bzl.plugins:agp_hook:7.4.2-bzl-1"
//        classpath "com.android.tools:r8:8.0.34"
        classpath("$deps.zp_tinker_build") {
            exclude group: 'com.android.tools.build'
        }
        classpath 'com.hihonor.mcs:asplugin:2.0.1.300'
        classpath 'com.bzl.oneapk:oneapk-compress-plugin:1.3.2'
    }
}

plugins {
    alias(libs.plugins.compose.compiler) apply false
}

//1.指定版本:指定哪些版本的用户需要升级
//2.指定版本范围:指定需要升级版本范围[最小版本号]<= version <[最大版本号]
//3.优先级:当一个用户有多个升级时，优先展示哪个升级则优先展示的数字更大，如:260版本对应的优先级是102000，270则保证比102000大即可4.版本号需要填数字，对应规则:2060300->2.6.3、2110000 ->2.11.0
// appVersionCode计算方式:主版本号*1000000+次版本号*10000+修订版本号*100
//最后两位是用来标记状态的
// 00：开发版本，开发期间都是这个
// 01~09：灰度版本，beta1~beta9
// 10：正式版本，到时候上传hulk的脚本会校验是否是10
apply from: "$rootDir/gradle/submitqa.groovy"
ext {
    appVersionCode = 2180000
    appVersionName = genVersionName(appVersionCode)
    appShowVersionName = genShowName(appVersionCode)
    channelString = genChannel(appVersionCode)
    patchBaseVersionName = "1.1.2"
    isPrintLog = true
    serverConfig = 2
    minifyEnable = false
    applicationId = "com.kanzhun.marry"
}
println("""
------------------------请确认版本信息------------------------
#请务必是key: value的形式，jenkins会通过它来读取版本号
appVersionCode: ${project.ext.appVersionCode}
appVersionName: ${project.ext.appVersionName}
appShowVersionName: ${project.ext.appShowVersionName}
channelString: ${project.ext.channelString}
patchBaseVersionName: ${project.ext.patchBaseVersionName}
------------------------------------------------------------
""")

task clean(type: Delete) {
    delete rootProject.buildDir
}

subprojects {
    project.configurations.all {
        resolutionStrategy.eachDependency { details ->
//            def requested = details.requested
//            if (requested.group == 'androidx.lifecycle') {
//                if (requested.name.startsWith("lifecycle-extensions")){
//                    details.useVersion "2.2.0"
//                }else {
//                    println "2.4.0"
//                    details.useVersion versions.lifecycle
//                }
//                details.useVersion versions.lifecycle
//            }
            if (details.requested.group == 'androidx.fragment') {
                details.useVersion versions.fragment
            }
            if (details.requested.group == 'androidx.activity') {
                details.useVersion versions.activity
            }
            //test okgo包内okhttp 是3.8.1 版本不一致
            if (details.requested.group == 'com.squareup.okhttp3') {
                details.useVersion versions.okhttp3
            }
        }
    }
}