apply from: 'versions.gradle'
dependencyResolutionManagement {
    repositoriesMode.set(RepositoriesMode.FAIL_ON_PROJECT_REPOS)
    addRepos(repositories)
    repositories {
        flatDir {
            dirs "libs"
        }
        // 配置HMS Core SDK的Maven仓地址。
        maven { url 'https://developer.huawei.com/repo/' }
        mavenCentral()
    }

}
rootProject.name = "com.kanzhun.orange"
include ':app'
include ':lib_foundation'
include ':lib_common'
include ':lib_utils'
include ':lib_mqtt'
include ':lib_push'
//include ':lib_http'
include ':lib_webview'
include ':lib_http_retrofit'
include ':lib_imageviewer'
include ':lib_keyboard'
include ':lib_imageviewer'
include ':lib_keyboard'
include ':lib_startup'
include ':module_me'
include ':module_chat'
include ':module_matching'
include ':module_video_meeting'
include ':lib_pic_video_take'
include ':moudle_social'
//include ':lib_luban'
include ':blurkit'
include ':module_parent'
//include ':module_new_task'
include ':lib_share'
include ':libSafe'
include ':floatingx'

try {
    Properties properties = new Properties()
    File file = new File(rootProject.projectDir, "local.properties")
    properties.load(new FileInputStream(file))
    if (properties.getProperty("includeSafe", "false") == "true") {
        include ':lib_safe'
    }
} catch (ignored) {
}

include ':module_activity'

//include ':lib_kuikly'
