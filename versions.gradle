ext.deps = [:]

def build_versions = [:]
ext.build_versions = build_versions
build_versions.min_sdk = 24
build_versions.target_sdk = 33
build_versions.compile_sdk = 35
build_versions.build_tools = "35.0.0"

def versions = [:]
versions.wmrouter = "1.2.1-sort-beta1"

versions.lifecycle = "2.7.0"
versions.fragment = "1.7.1"
versions.activity = "1.9.1"
versions.appcompat = "1.6.1"
versions.annotation = "1.8.1"
versions.constraint_layout = "2.2.1"
versions.recyclerview = "1.3.2"
versions.cardview = "1.0.0"
versions.core = "1.13.1"
versions.navigation = "2.7.7"
versions.gallery = "0.3.77-beta10.1"
versions.camera = "0.3.76_beta29"
versions.brvah = "3.0.7"

versions.room = "2.6.1"
versions.wcdb_room = "1.0.8"
versions.mmkv = "1.2.12"
versions.qmui = "2.0.1"
versions.okhttp3 = "4.9.3"
versions.gson = "2.8.8"
versions.glide = "5.0.0-rc01"
versions.glide_transformations = "4.3.0"
versions.glide_gpuimage = "2.1.0"
versions.glide_webp = "2.0.4.13.1"

versions.huawei_push = "6.12.0.300"

versions.tlog = "1.1.12"
versions.protobuf_format = "1.4"
versions.protobuf = "3.25.0"

versions.bugly_report = "4.1.9.3"
versions.bugly_native = "3.9.2"
versions.qbusict = "2.2.0"

versions.rxjava = "3.1.3"
versions.rxandroid = "3.0.0"
versions.retrofit = "2.9.0"

versions.ktx = "1.13.1"
versions.kotlin = "1.9.23"
versions.sketch = "2.7.1"  //查看大图

versions.smartrefresh = "2.1.1"
versions.magic_indicator = "1.7.0"

versions.lottie = "6.5.2"
versions.banner = "2.2.3"

versions.tinker_gradle_plugin = "1.2.0"
versions.zp_tinker_build = "********.beta1"
versions.zp_tinker_runtime = "********.beta1"

versions.walle = "1.0.3"
versions.shortcutbadger = "1.1.26"

versions.mi_push = "6.0.1-C_3rd"
versions.oppo_push = "3.5.2"
versions.vivo_push = "4.0.4.0_504"
versions.honor_push = "7.0.61.303"

versions.wechat = "6.8.0"
versions.alipaysdk = "15.8.10@aar"

versions.apm = "1.3.60-alpha40.5.2"

versions.net_util = "0.0.3"

//versions.zp_sdk_support = "9.8.2@aar"
//versions.zp_sdk_eagle = "10.4.1@aar"
//versions.zp_sdk_rtc = "9.8.4@aar"
//versions.zp_sdk_matrix = "9.8.1@aar"

versions.zp_sdk_support = "10.21.1"
versions.zp_sdk_eagle = "10.34.1"
versions.zp_sdk_rtc = "10.21.1"
versions.zp_sdk_matrix = "10.21.1"

versions.blurkit = "1.1.0"

versions.socket = "1.0.2"

versions.supter_textview="********"

versions.dsl_tablayout="3.5.3"
versions.ShadowLayout="3.3.3"

versions.anti = "2.0.95"


def deps = [:]
def androidx = [:]
deps.androidx = androidx
androidx.app_compat = "androidx.appcompat:appcompat:$versions.appcompat"
androidx.annotations = "androidx.annotation:annotation:$versions.annotation"
androidx.constraint_layout = "androidx.constraintlayout:constraintlayout:$versions.constraint_layout"
androidx.recyclerview = "androidx.recyclerview:recyclerview:$versions.recyclerview"
androidx.cardview = "androidx.cardview:cardview:$versions.cardview"
androidx.core = "androidx.core:core:$versions.core"
//androidx.core_animation = "androidx.core:core-animation:1.0.0-alpha02"
androidx.navigation_fragment = "androidx.navigation:navigation-fragment:$versions.navigation"
androidx.navigation_ui = "androidx.navigation:navigation-ui:$versions.navigation"
androidx.ktx = "androidx.core:core-ktx:$versions.ktx"
androidx.lifecycle = "androidx.lifecycle:lifecycle-viewmodel:$versions.lifecycle"
androidx.lifecycle_ktx = "androidx.lifecycle:lifecycle-viewmodel-ktx:$versions.lifecycle"


deps.androidx = androidx

def support = [:]
support.design = "com.google.android.material:material:1.2.0"
support.flexbox = 'com.google.android:flexbox:2.0.1'
deps.support = support

def kotlin = [:]
kotlin.stdLib = "org.jetbrains.kotlin:kotlin-stdlib:$versions.kotlin"
deps.kotlin = kotlin

def room = [:]
deps.room = room
room.compiler = "androidx.room:room-compiler:$versions.room"
room.runtime = "androidx.room:room-runtime:$versions.room"
room.wcdb_room = "com.tencent.wcdb:room:$versions.wcdb_room"
room.wcdb_android = "com.tencent.wcdb:wcdb-android:$versions.wcdb_room"

def glide = [:]
glide.glide = "com.github.bumptech.glide:glide:$versions.glide"
glide.glide_okhttp = "com.github.bumptech.glide:okhttp3-integration:$versions.glide"
glide.glide_compiler = "com.github.bumptech.glide:compiler:$versions.glide"
glide.glide_transformations = "jp.wasabeef:glide-transformations:$versions.glide_transformations"
//glide.glide_gpuimage = "jp.co.cyberagent.android:gpuimage:$versions.glide_gpuimage"
glide.glide_webp = "com.github.zjupure:webpdecoder:$versions.glide_webp"
deps.glide = glide

def bugly = [:]
bugly.report = "com.tencent.bugly:crashreport:$versions.bugly_report"
bugly.bugly_native = "com.tencent.bugly:nativecrashreport:$versions.bugly_native"
deps.zp_tinker_runtime = "com.twl.zhipin:tinker-runtime:$versions.zp_tinker_runtime"
deps.bugly = bugly

def rxjava = [:]
rxjava.rxjava = "io.reactivex.rxjava3:rxjava:$versions.rxjava"
rxjava.rxandroid = "io.reactivex.rxjava3:rxandroid:$versions.rxandroid"
deps.rxjava = rxjava

def retrofit = [:]
retrofit.retrofit = "com.squareup.retrofit2:retrofit:$versions.retrofit"
retrofit.retrofit_gosn = "com.squareup.retrofit2:converter-gson:$versions.retrofit"
retrofit.retrofit_adapter = "com.squareup.retrofit2:adapter-rxjava3:$versions.retrofit"
deps.retrofit = retrofit

def sketch = [:]
sketch.sketch = "io.github.panpf.sketch:sketch:$versions.sketch"
sketch.sketch_gif = "io.github.panpf.sketch:sketch-gif:$versions.sketch"
deps.sketch = sketch

def smartrefresh = [:]
smartrefresh.smartrefresh_kernel = "io.github.scwang90:refresh-layout-kernel:$versions.smartrefresh"
smartrefresh.refresh_header_classics = "io.github.scwang90:refresh-header-classics:$versions.smartrefresh"
deps.smartrefresh = smartrefresh

deps.wmrouter = "io.github.meituan-dianping:router:$versions.wmrouter"
deps.wmrouter_compiler = "io.github.meituan-dianping:compiler:$versions.wmrouter"
deps.wmrouter_gradle_plugin = "io.github.meituan-dianping:plugin:$versions.wmrouter"
deps.mmkv = "com.tencent:mmkv-static:$versions.mmkv"
deps.qmui = "com.qmuiteam:qmui:$versions.qmui"
deps.okhttp3 = "com.squareup.okhttp3:okhttp:$versions.okhttp3"
deps.okhttp3_log = "com.squareup.okhttp3:logging-interceptor:$versions.okhttp3"
deps.gson = "com.google.code.gson:gson:$versions.gson"
deps.gallery = "com.hpbr.bosszhipin:module-gallery:$versions.gallery"
deps.camera = "com.hpbr.bosszhipin:module-camera:$versions.camera"
deps.brvah = "com.github.CymChad:BaseRecyclerViewAdapterHelper:$versions.brvah"

def huawei = [:]
deps.huawei_push = "com.huawei.hms:push:$versions.huawei_push"
deps.huawei = huawei

deps.tlog = "com.techwolf.lib:tlog:$versions.tlog"
deps.qbusict = "nl.qbusict:cupboard:$versions.qbusict"

deps.protobuf_format = "com.googlecode.protobuf-java-format:protobuf-java-format:$versions.protobuf_format"
deps.protobuf = "com.google.protobuf:protobuf-java:$versions.protobuf"

deps.magic_indicator = "com.github.hackware1993:MagicIndicator:$versions.magic_indicator"

deps.lottie = "com.airbnb.android:lottie:$versions.lottie"
deps.lottie = "com.airbnb.android:lottie-compose:$versions.lottie"
deps.banner = "io.github.youth5201314:banner:$versions.banner"

deps.zp_tinker_build = "com.twl.zhipin:tinker-build:$versions.zp_tinker_build"
deps.zp_tinker_runtime = "com.twl.zhipin:tinker-runtime:$versions.zp_tinker_runtime"

deps.walle = "com.meituan.android.walle:payload_reader:$versions.walle"

deps.shortcutbadger = "com.twl.zhipin:shortcutbadger:$versions.shortcutbadger"

deps.mi_push = "com.twl.sdk.file:MiPush_SDK_Client:$versions.mi_push"
deps.oppo_push = "com.twl.sdk.file:heytap-msp:$versions.oppo_push"
deps.vivo_push = "com.twl.sdk.file:vivo_pushsdk:$versions.vivo_push"
deps.honor_push = "com.hihonor.mcs:push:$versions.honor_push"
deps.wechat = "com.tencent.mm.opensdk:wechat-sdk-android:$versions.wechat"
deps.alipaysdk = "com.alipay.sdk:alipaysdk-android:$versions.alipaysdk"

def apm = [:]
apm.core = "com.hpbr.bosszhipin:module-apm:$versions.apm" // APM 核心模块
apm.settings = "com.hpbr.bosszhipin:module-apm-settings:$versions.apm" // APM 设置模块（内存监控、帧率监控等项目的集成和配置）
deps.apm = apm

deps.net_util = "com.twl.zhipin:net-util:$versions.net_util"

deps.zp_sdk_support = "com.twl.sdk.file:zp-sdk-support:$versions.zp_sdk_support"//基础能力，底层通信能力
deps.zp_sdk_eagle = "com.twl.sdk.file:zp-sdk-eagle:$versions.zp_sdk_eagle"//统计能力
deps.zp_sdk_rtc = "com.twl.sdk.file:zp-sdk-rtc:$versions.zp_sdk_rtc"//音视频能力
deps.zp_sdk_matrix = "com.twl.sdk.file:zp-sdk-matrix:$versions.zp_sdk_matrix"//特效、降噪、视频美颜

deps.blurkit = "io.alterac.blurkit:blurkit:$versions.blurkit"

deps.socket = "io.socket:socket.io-client:$versions.socket"

deps.super_textview = "com.github.chenBingX:SuperTextView:$versions.supter_textview"

deps.ShadowLayout = "com.github.lihangleo2:ShadowLayout:$versions.ShadowLayout"

deps.photoView = "com.github.chrisbanes:PhotoView:2.0.0"
//文档 ： https://github.com/TxcA/SpannableX?tab=readme-ov-file
deps.spannablex = "com.itxca.spannablex:spannablex:1.0.4"

deps.anti = "com.twl.anti:anti:$versions.anti"

deps.zxing = "com.google.zxing:core:3.5.3"

deps.tinker_gradle_plugin = "com.tencent.bugly:tinker-support:$versions.tinker_gradle_plugin"

ext.deps = deps
ext.versions = versions

def addPluginRepos(RepositoryHandler handler) {
    handler.maven { url uri("$rootDir.absolutePath/.local") }
    //如需添加其他仓库，请使用android3 nexus代理缓存，避免直接使用三方仓库
    handler.flatDir { dirs "$projectDir/libs" }
    handler.maven { url "https://android3.weizhipin.com/nexus/repository/public/" }
    handler.maven { url 'https://maven.aliyun.com/nexus/content/groups/public/' }
    handler.maven { url 'https://maven.aliyun.com/repository/central' }
    handler.maven { url 'https://maven.aliyun.com/repository/google' }
    handler.maven { url 'https://maven.aliyun.com/repository/jcenter' }
    // 配置HMS Core SDK的Maven仓地址。
    handler.maven { url 'https://developer.huawei.com/repo/' }
    // 配置荣耀 push SDK的Maven仓地址。
    handler.maven { url 'https://developer.hihonor.com/repo'}
}

def addRepos(RepositoryHandler handler) {
    handler.maven { url uri("$rootDir.absolutePath/.local") }
    //如需添加其他仓库，请使用android3 nexus代理缓存，避免直接使用三方仓库
    //todo flatDir { dirs "$projectDir/libs" } 报错
//    handler.flatDir { dirs "$projectDir/libs" }
    handler.maven { url "https://android3.weizhipin.com/nexus/repository/public/" }
    handler.maven { url 'https://maven.aliyun.com/nexus/content/groups/public/' }
    handler.maven { url 'https://maven.aliyun.com/repository/central' }
    handler.maven { url 'https://maven.aliyun.com/repository/google' }
    handler.maven { url 'https://maven.aliyun.com/repository/jcenter' }
    handler.maven { url 'https://jitpack.io' }
    // 配置HMS Core SDK的Maven仓地址。
    handler.maven { url 'https://developer.huawei.com/repo/' }
    // 配置荣耀 push SDK的Maven仓地址。
    handler.maven { url 'https://developer.hihonor.com/repo' }
}

ext.addRepos = this.&addRepos
ext.addPluginRepos = this.&addPluginRepos