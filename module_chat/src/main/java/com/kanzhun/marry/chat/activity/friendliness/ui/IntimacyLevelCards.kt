package com.kanzhun.marry.chat.activity.friendliness.ui

import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.tween
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.constraintlayout.compose.Dimension
import androidx.constraintlayout.compose.Visibility
import coil.compose.AsyncImage
import com.kanzhun.common.base.compose.ext.boldFontFamily
import com.kanzhun.common.base.compose.ext.noRippleClickable
import com.kanzhun.common.base.compose.theme.O2Theme
import com.kanzhun.common.base.compose.ui.O2ProgressBar
import com.kanzhun.common.base.compose.ui.ProgressOrientation
import com.kanzhun.marry.chat.R
import com.kanzhun.marry.chat.api.response.IntimateLevel
import com.techwolf.lib.tlog.TLog
import kotlin.math.absoluteValue

// 等级颜色定义 - 根据用户等级动态调整UI组件颜色
private val levelColors = mapOf(
    1 to Color(0xFFCF99FF),
    2 to Color(0xFFEC8CFF),
    3 to Color(0xFFFF85F7),
    4 to Color(0xFFFF8CC7),
    5 to Color(0xFFFF988C)
)

/**
 * 根据等级获取对应颜色
 * @param level 亲密度等级
 * @return 对应的颜色，如果等级不在范围内则返回默认颜色
 */
private fun getLevelColor(level: Int): Color {
    return levelColors[level] ?: Color(0xFFCF99FF) // 默认使用等级1的颜色
}

/**
 * 根据等级获取标题图片资源ID
 * @param level 亲密度等级
 * @return 对应的标题图片资源ID
 */
private fun getTitleImageResource(level: Int): Int {
    return when (level) {
        1 -> R.mipmap.chat_ic_friendliness_title_level_1
        2 -> R.mipmap.chat_ic_friendliness_title_level_2
        3 -> R.mipmap.chat_ic_friendliness_title_level_3
        4 -> R.mipmap.chat_ic_friendliness_title_level_4
        5 -> R.mipmap.chat_ic_friendliness_title_level_5
        else -> R.mipmap.chat_ic_friendliness_title_level_1 // 默认使用等级1的标题图片
    }
}

/**
 * 根据等级获取卡片背景图片资源ID
 * @param level 亲密度等级
 * @return 对应的卡片背景图片资源ID
 */
private fun getCardBackgroundResource(level: Int): Int {
    return when (level) {
        1 -> R.mipmap.chat_ic_friendliness_card_bg_1
        2 -> R.mipmap.chat_ic_friendliness_card_bg_2
        3 -> R.mipmap.chat_ic_friendliness_card_bg_3
        4 -> R.mipmap.chat_ic_friendliness_card_bg_4
        5 -> R.mipmap.chat_ic_friendliness_card_bg_5
        else -> R.mipmap.chat_ic_friendliness_card_bg_1 // 默认使用等级1的背景
    }
}

/**
 * 根据等级获取花朵装饰图片资源ID
 * @param level 亲密度等级
 * @return 对应的花朵装饰图片资源ID
 */
private fun getFlowerResource(level: Int): Int {
    return when (level) {
        1 -> R.mipmap.chat_ic_flower_1
        2 -> R.mipmap.chat_ic_flower_2
        3 -> R.mipmap.chat_ic_flower_3
        4 -> R.mipmap.chat_ic_flower_4
        5 -> R.mipmap.chat_ic_flower_5
        else -> R.mipmap.chat_ic_flower_1 // 默认使用等级1的花朵
    }
}

/**
 * 亲密等级卡片组件
 */
@OptIn(ExperimentalFoundationApi::class)
@Composable
fun IntimacyLevelCards(
    intimateLevelList: List<IntimateLevel>,
    currentLevel: Int,
    modifier: Modifier = Modifier,
    onUpgradeClick: ((String) -> Unit)? = null // 添加升级按钮点击回调
) {
    // 找到当前等级在列表中的索引
    val currentLevelIndex = intimateLevelList.indexOfFirst { it.targetLevel == currentLevel }.let {
        if (it == -1) 0 else it
    }

    // 创建 PagerState，初始页面为当前等级
    val pagerState = rememberPagerState(
        initialPage = currentLevelIndex,
        pageCount = { intimateLevelList.size }
    )

    Column(
        modifier = modifier
            .fillMaxWidth()
    ) {
        // 标题区域 - 根据当前等级显示不同的标题图片
        TitleSection(currentLevel = currentLevel)

        // 横向滑动卡片
        HorizontalPager(
            state = pagerState,
            modifier = Modifier.fillMaxWidth(),
            contentPadding = PaddingValues(horizontal = 28.dp),
        ) { page ->
            val intimateLevel = intimateLevelList[page]
            val isCurrentLevel = intimateLevel.targetLevel == currentLevel

            // 计算当前页面与中心页面的距离，用于缩放动画
            val pageOffset = (pagerState.currentPage - page) + pagerState.currentPageOffsetFraction
            val scale by animateFloatAsState(
                targetValue = if (pageOffset.absoluteValue < 0.5f) 1.0f else 0.92f,
                animationSpec = tween(300),
                label = "scale"
            )

            IntimacyLevelCard(
                currentLevel = currentLevel,
                intimateLevel = intimateLevel,
                isCurrentLevel = isCurrentLevel,
                page = page,
                scale = scale,
                onUpgradeClick = onUpgradeClick,
                modifier = Modifier.fillMaxWidth()
            )
        }
    }
}

/**
 * 标题区域组件
 * @param currentLevel 当前亲密度等级，用于显示对应的标题图片
 */
@Composable
private fun TitleSection(currentLevel: Int) {
    // 根据当前等级获取对应的标题图片资源
    val titleImageResource = getTitleImageResource(currentLevel)

    TLog.info(
        "TitleSection",
        "根据等级显示标题图片: currentLevel=$currentLevel, titleImageResource=$titleImageResource"
    )

    Image(
        painter = painterResource(titleImageResource),
        contentDescription = "亲密度等级标题",
        modifier = Modifier
            .padding(start = 20.dp)
            .size(width = 96.dp, height = 34.dp)
    )
}

/**
 * 单个亲密等级卡片
 */
@Composable
private fun IntimacyLevelCard(
    modifier: Modifier = Modifier,
    currentLevel: Int = 0,
    intimateLevel: IntimateLevel,
    isCurrentLevel: Boolean,
    page: Int,
    scale: Float,
    onUpgradeClick: ((String) -> Unit)? = null, // 添加升级按钮点击回调
) {
    // 获取当前等级对应的颜色
    val levelColor = getLevelColor(currentLevel)

    TLog.info(
        "IntimacyLevelCard",
        "渲染亲密等级卡片: level=${intimateLevel.targetLevel}, color=$levelColor, 添加底部阴影效果"
    )

    ConstraintLayout(
        modifier = modifier
            .graphicsLayer {
                scaleX = scale
                scaleY = scale
            }
            .fillMaxWidth()
    ) {
        val (bg, shadow, title, content, current, upgrade, flower) = createRefs()

        // 底部阴影效果 - 手动绘制渐变阴影
        Image(
            painter = painterResource(R.mipmap.chat_ic_level_card_shadow),
            contentDescription = null,
            modifier = Modifier
                .constrainAs(shadow) {
                    start.linkTo(parent.start, 28.dp)
                    end.linkTo(parent.end, 28.dp)
                    bottom.linkTo(parent.bottom)

                    width = Dimension.fillToConstraints
                }
                .offset(y = 16.dp)
        )

        // 卡片背景 - 根据等级使用不同的背景图片
        Image(
            painter = painterResource(id = getCardBackgroundResource(currentLevel)),
            contentDescription = "卡片背景",
            contentScale = ContentScale.FillBounds,
            modifier = Modifier
                .padding(top = 20.dp)
                .constrainAs(bg) {
                    start.linkTo(parent.start)
                    end.linkTo(parent.end)
                    top.linkTo(parent.top)
                    width = Dimension.fillToConstraints
                    height = Dimension.ratio("327:189")
                }
        )

        // 等级标识 - 显示等级数字
        Text(
            text = "Lv.${intimateLevel.targetLevel}",
            color = levelColor,
            fontSize = 12.sp,
            fontWeight = FontWeight.Bold,
            modifier = Modifier
                .background(
                    color = Color.White.copy(alpha = 0.9f),
                    shape = RoundedCornerShape(8.dp)
                )
                .padding(horizontal = 6.dp, vertical = 2.dp)
                .constrainAs(flower) {
                    end.linkTo(bg.end, 16.dp)
                    top.linkTo(bg.top, 16.dp)
                }
        )

        // 等级标题
        Text(
            text = intimateLevel.title ?: "",
            style = TextStyle(
                fontSize = 32.sp,
                fontFamily = boldFontFamily(),
                fontWeight = FontWeight(700),
                color = Color(0xFF013D3E),
            ),
            textAlign = TextAlign.Center,
            modifier = Modifier
                .constrainAs(title) {
                    start.linkTo(content.start)
                    bottom.linkTo(content.top, 12.dp)
                }
        )

        Column(
            modifier = Modifier
                .constrainAs(content) {
                    start.linkTo(bg.start, 24.dp)
                    end.linkTo(upgrade.start, 10.dp, goneMargin = 48.dp)
                    bottom.linkTo(bg.bottom, 20.dp)

                    width = Dimension.fillToConstraints
                },
        ) {
            // 等级描述
            Text(
                text = intimateLevel.content ?: "",
                style = TextStyle(
                    fontSize = 14.sp,
                    fontWeight = FontWeight(400),
                    color = Color(0xFF7F7F7F),
                )
            )

            Spacer(modifier = Modifier.height(8.dp))

            // 进度条 - 使用基于等级的颜色
            O2ProgressBar(
                modifier = Modifier.fillMaxWidth(),
                progress = intimateLevel.progress / 100f,
                orientation = ProgressOrientation.Horizontal,
                thickness = 8.dp,
                progressBrush = Brush.linearGradient(
                    0.0f to levelColor,
                    1.0f to levelColor
                )
            )

            Spacer(modifier = Modifier.height(4.dp))

            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text(
                    text = "LV$page",
                    style = TextStyle(
                        fontSize = 12.sp,
                        fontWeight = FontWeight(400),
                        color = Color(0xFF7F7F7F),
                    )
                )

                Text(
                    text = "LV${page + 1}",
                    style = TextStyle(
                        fontSize = 12.sp,
                        fontWeight = FontWeight(400),
                        color = Color(0xFF7F7F7F),
                    )
                )
            }
        }

        // 当前等级标识
        Image(
            painter = painterResource(id = R.mipmap.chat_ic_friendliness_current_level),
            contentDescription = "image description",
            contentScale = ContentScale.FillBounds,
            modifier = Modifier
                .padding(top = 20.dp)
                .width(56.dp)
                .aspectRatio(56f / 42)
                .constrainAs(current) {
                    start.linkTo(parent.start)
                    top.linkTo(parent.top)

                    visibility = if (isCurrentLevel) Visibility.Visible else Visibility.Gone
                }
        )

        // 判断是否显示"去升级"按钮
        // 只有当前等级或未开始的等级才显示"去升级"按钮
        // 当进度达到100%时，不显示升级按钮
        val shouldShowUpgradeButton = intimateLevel.progress < 100

        TLog.debug(
            "IntimacyLevelCard",
            "升级按钮显示判断: targetLevel=${intimateLevel.targetLevel}, currentLevel=$currentLevel, progress=${intimateLevel.progress}, shouldShow=$shouldShowUpgradeButton"
        )

        Text(
            text = "去升级",
            style = TextStyle(
                fontSize = 14.sp,
                fontWeight = FontWeight(500),
                color = Color(0xFFFFFFFF),
                textAlign = TextAlign.Center,
            ),
            modifier = Modifier
                .background(color = Color(0xFF034C4D), shape = RoundedCornerShape(size = 25.dp))
                .padding(horizontal = 15.dp, vertical = 5.dp)
                .noRippleClickable {
                    // 点击"去升级"按钮的处理逻辑
                    TLog.info(
                        "IntimacyLevelCard",
                        "点击去升级按钮，当前等级: ${intimateLevel.targetLevel}"
                    )
                    onUpgradeClick?.invoke("upgrade_level_${intimateLevel.targetLevel}")
                }
                .constrainAs(upgrade) {
                    end.linkTo(parent.end, 20.dp)
                    bottom.linkTo(parent.bottom, 28.dp)

                    visibility =
                        if (shouldShowUpgradeButton) Visibility.Visible else Visibility.Gone
                }
        )

        // 等级装饰图片 - 根据解锁状态选择不同的图片字段
        // 判断是否为未开始的等级（需要置灰显示）
        val isNotStartedLevel = intimateLevel.targetLevel > currentLevel
        // 根据解锁状态选择图片URL：未解锁使用lockImg，已解锁使用img
        val flowerImageUrl = if (isNotStartedLevel) {
            intimateLevel.lockImg // 未解锁的等级使用lockImg字段
        } else {
            intimateLevel.img // 已解锁的等级使用img字段
        }
        val flowerPlaceholderResource = getFlowerResource(intimateLevel.targetLevel)
        TLog.debug(
            "IntimacyLevelCard",
            "加载等级装饰图片: url=$flowerImageUrl, level=${intimateLevel.targetLevel}, currentLevel=$currentLevel, isNotStarted=$isNotStartedLevel, 使用字段=${if (isNotStartedLevel) "lockImg" else "img"}, placeholder=$flowerPlaceholderResource"
        )

        if (!flowerImageUrl.isNullOrEmpty()) {
            // 加载网络图片
            AsyncImage(
                model = flowerImageUrl,
                contentDescription = "等级装饰图片",
                contentScale = ContentScale.Fit,
                modifier = Modifier
                    .size(108.dp)
                    .padding(10.dp)
                    .constrainAs(flower) {
                        end.linkTo(parent.end, 20.dp)
                        top.linkTo(parent.top)
                    }
            )
        } else {
            // 使用根据等级的占位图片
            Image(
                painter = painterResource(id = flowerPlaceholderResource),
                contentDescription = "等级${intimateLevel.targetLevel}装饰图片",
                contentScale = ContentScale.Fit,
                modifier = Modifier
                    .size(108.dp)
                    .padding(10.dp)
                    .constrainAs(flower) {
                        end.linkTo(parent.end, 20.dp)
                        top.linkTo(parent.top)
                    }
            )
        }
    }
}

@Preview(showBackground = true, heightDp = 360)
@Composable
private fun IntimacyLevelCardsPreview() {
    O2Theme {
        IntimacyLevelCards(
            intimateLevelList = listOf(
                IntimateLevel(
                    title = "初遇心动",
                    content = "刚刚相识，心中有了小小的悸动",
                    targetLevel = 1,
                    progress = 100,
//                    img = "https://img.bosszhipin.com/static/kanzhun/server/user-Background-Img/level0.png"
                ),
                IntimateLevel(
                    title = "日常牵挂",
                    content = "开始关心对方的日常，想要了解更多",
                    targetLevel = 2,
                    progress = 100,
//                    img = "https://img.bosszhipin.com/static/kanzhun/server/user-Background-Img/level1.png"
                ),
                IntimateLevel(
                    title = "灵魂共鸣",
                    content = "发现彼此有很多共同话题和兴趣",
                    targetLevel = 3,
                    progress = 70,
//                    img = "https://img.bosszhipin.com/static/kanzhun/server/user-Background-Img/level2.png"
                ),
                IntimateLevel(
                    title = "彼此依赖",
                    content = "已经习惯了有对方的陪伴，互相依赖",
                    targetLevel = 4,
                    progress = 0,
//                    img = "https://img.bosszhipin.com/static/kanzhun/server/user-Background-Img/level3.png"
                ),
                IntimateLevel(
                    title = "长久陪伴",
                    content = "愿意与对方携手走过人生的每一个阶段",
                    targetLevel = 5,
                    progress = 0,
//                    img = "https://img.bosszhipin.com/static/kanzhun/server/user-Background-Img/level4.png"
                )
            ),
            currentLevel = 3,
            onUpgradeClick = { upgradeType ->
                // Preview中的点击事件处理
                TLog.info("Preview", "点击升级: $upgradeType")
            }
        )
    }
}

@Preview(showBackground = true, heightDp = 360)
@Composable
private fun IntimacyLevelCardPreview() {
    O2Theme {
        IntimacyLevelCard(
            intimateLevel = IntimateLevel(
                title = "灵魂共鸣",
                content = "发现彼此有很多共同话题和兴趣",
                targetLevel = 2,
                progress = 90,
//                img = "https://img.bosszhipin.com/static/kanzhun/server/user-Background-Img/level2.png"
            ),
            isCurrentLevel = true,
            page = 2,
            scale = 1.0f,
            onUpgradeClick = { upgradeType ->
                // Preview中的点击事件处理
                TLog.info("Preview", "点击升级: $upgradeType")
            },
            modifier = Modifier.padding(16.dp)
        )
    }
}

@Preview(showBackground = true)
@Composable
private fun TitleSectionPreview() {
    O2Theme {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // 显示不同等级的标题图片
            Text("等级 0 (Lv1):", fontSize = 14.sp, color = Color.Gray)
            TitleSection(currentLevel = 0)

            Text("等级 1 (Lv2):", fontSize = 14.sp, color = Color.Gray)
            TitleSection(currentLevel = 1)

            Text("等级 2 (Lv3):", fontSize = 14.sp, color = Color.Gray)
            TitleSection(currentLevel = 2)

            Text("等级 3 (Lv4):", fontSize = 14.sp, color = Color.Gray)
            TitleSection(currentLevel = 3)

            Text("等级 4 (Lv5):", fontSize = 14.sp, color = Color.Gray)
            TitleSection(currentLevel = 4)
        }
    }
}
