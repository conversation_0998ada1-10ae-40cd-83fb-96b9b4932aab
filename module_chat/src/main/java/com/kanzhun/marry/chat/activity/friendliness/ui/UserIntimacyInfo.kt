package com.kanzhun.marry.chat.activity.friendliness.ui

import androidx.compose.animation.core.FastOutSlowInEasing
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.tween
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.rotate
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import coil.compose.AsyncImage
import com.kanzhun.common.base.compose.theme.O2Theme
import com.kanzhun.marry.chat.R
import com.techwolf.lib.tlog.TLog
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

// 等级颜色定义 - 与其他文件保持一致
private val levelColors = mapOf(
    1 to Color(0xFFCF99FF),
    2 to Color(0xFFEC8CFF),
    3 to Color(0xFFFF85F7),
    4 to Color(0xFFFF8CC7),
    5 to Color(0xFFFF988C)
)

/**
 * 根据等级获取对应颜色
 * @param level 亲密度等级
 * @return 对应的颜色，如果等级不在范围内则返回默认颜色
 */
private fun getLevelColor(level: Int): Color {
    return levelColors[level] ?: Color(0xFFCF99FF) // 默认使用等级1的颜色
}


/**
 * 用户相识信息模块
 */
@Composable
fun UserIntimacyInfo(
    userAvatar: String?,
    userNickName: String?,
    friendAvatar: String?,
    friendNickName: String?,
    intimateLevel: Int,
    intimateValue: Int,
    daysKnown: Int,
    chatStartDate: Long,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp)
    ) {
        Spacer(modifier = Modifier.height(36.dp))

        // 双方用户头像和昵称信息
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.Center,
            verticalAlignment = Alignment.Bottom
        ) {
            // 用户头像和昵称
            Column(
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                AsyncImage(
                    model = userAvatar,
                    contentDescription = "用户头像",
                    modifier = Modifier
                        .size(48.dp)
                        .clip(CircleShape),
                    contentScale = ContentScale.Crop,
                    placeholder = painterResource(R.mipmap.common_default_avatar),
                    error = painterResource(R.mipmap.common_default_avatar)
                )

                Spacer(modifier = Modifier.height(12.dp))

                Text(
                    text = userNickName ?: "",
                    color = colorResource(R.color.common_color_7F7F7F),
                    fontSize = 14.sp
                )
            }

            Spacer(modifier = Modifier.width(43.dp))

            // 亲密度数值和图标
            Column(
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                // 获取当前等级对应的颜色
                val levelColor = getLevelColor(intimateLevel)
                // 计算目标进度值（0.0-1.0）
                val targetProgress = intimateValue / 100f

                // 动画状态管理
                var animationStarted by remember { mutableStateOf(false) }

                // 动画进度值 - 从0开始上涨到目标进度
                val animatedProgress by animateFloatAsState(
                    targetValue = if (animationStarted) targetProgress else 0f,
                    animationSpec = tween(
                        durationMillis = 2000, // 2秒动画时长
                        easing = FastOutSlowInEasing // 快进慢出的缓动效果
                    ),
                    label = "heart_progress_animation"
                )

                // 启动动画效果
                LaunchedEffect(intimateValue) {
                    TLog.info("UserIntimacyInfo", "启动心形进度条液体上涨动画: intimateValue=$intimateValue")
                    animationStarted = true
                }

                TLog.info(
                    "UserIntimacyInfo",
                    "渲染亲密度心形动画: level=$intimateLevel, targetProgress=$targetProgress, animatedProgress=$animatedProgress, color=$levelColor"
                )

                // 心形动画进度条 - 基于等级动态显示进度和颜色，带有液体上涨动画效果
                HeartProgressIndicator(
                    progress = animatedProgress,
                    waterColor = levelColor,
                    heartColor = levelColor.copy(alpha = 0.8f),
                    modifier = Modifier.size(72.dp)
                )

                Spacer(modifier = Modifier.height(12.dp))

                Text(
                    text = "${intimateValue}%",
                    color = colorResource(R.color.common_color_191919),
                    fontSize = 20.sp,
                    fontWeight = FontWeight.SemiBold
                )
            }

            Spacer(modifier = Modifier.width(43.dp))

            // 对方头像和昵称
            Column(
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                AsyncImage(
                    model = friendAvatar,
                    contentDescription = "对方头像",
                    modifier = Modifier
                        .size(48.dp)
                        .clip(CircleShape),
                    contentScale = ContentScale.Crop,
                    placeholder = painterResource(R.mipmap.common_default_avatar),
                    error = painterResource(R.mipmap.common_default_avatar)
                )

                Spacer(modifier = Modifier.height(12.dp))

                Text(
                    text = friendNickName ?: "",
                    color = colorResource(R.color.common_color_7F7F7F),
                    fontSize = 14.sp
                )
            }
        }

        Spacer(modifier = Modifier.height(24.dp))

        // 获取当前等级对应的颜色用于下拉箭头和背景
        val levelColor = getLevelColor(intimateLevel)
        TLog.info(
            "UserIntimacyInfo",
            "渲染下拉箭头和认识时间背景: level=$intimateLevel, color=$levelColor"
        )

        // 下拉箭头 - 基于等级动态着色
        Image(
            painter = painterResource(R.mipmap.chat_ic_meeting_arrow_down),
            contentDescription = "下拉箭头",
            colorFilter = ColorFilter.tint(levelColor), // 根据用户等级动态着色
            modifier = Modifier
                .size(width = 32.dp, height = 7.dp)
                .rotate(180f)
                .alpha(0.1f)
                .align(Alignment.CenterHorizontally)
        )

        // 认识时间记录 - 背景色基于等级动态调整
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .background(
                    color = levelColor.copy(alpha = 0.1f), // 使用等级颜色，透明度10%
                    shape = RoundedCornerShape(16.dp)
                )
                .padding(24.dp)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "你们相识",
                    color = colorResource(R.color.common_color_7F7F7F),
                    fontSize = 14.sp
                )

                Spacer(modifier = Modifier.width(6.dp))

                Text(
                    text = daysKnown.toString(),
                    color = colorResource(R.color.common_color_191919),
                    fontSize = 24.sp,
                    fontWeight = FontWeight.Medium
                )

                Spacer(modifier = Modifier.width(6.dp))

                Text(
                    text = "天，起始日为 ",
                    color = colorResource(R.color.common_color_7F7F7F),
                    fontSize = 14.sp
                )

                Text(
                    text = formatDate(chatStartDate),
                    color = Color(0xFF191919),
                    fontWeight = FontWeight(600),
                    fontSize = 14.sp
                )
            }
        }
    }
}

/**
 * 格式化日期
 */
private fun formatDate(timestamp: Long): String {
    return if (timestamp > 0) {
        val sdf = SimpleDateFormat("yyyy.MM.dd", Locale.getDefault())
        sdf.format(Date(timestamp))
    } else {
        ""
    }
}

@Preview(showBackground = true)
@Composable
private fun UserIntimacyInfoPreview() {
    O2Theme {
        UserIntimacyInfo(
            userAvatar = null,
            userNickName = "Cathy 猫",
            friendAvatar = null,
            friendNickName = "刘先生",
            intimateLevel = 3,
            intimateValue = 47,
            daysKnown = 199,
            chatStartDate = 1637481600000L
        )
    }
}
