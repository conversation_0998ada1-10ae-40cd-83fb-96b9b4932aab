package com.kanzhun.marry.chat.compose

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.tween
import androidx.compose.animation.slideInVertically
import androidx.compose.animation.slideOutVertically
import androidx.compose.foundation.Image
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.constraintlayout.compose.Dimension
import coil.compose.AsyncImage
import com.kanzhun.common.base.compose.ext.noRippleClickable
import com.kanzhun.marry.chat.R
import com.techwolf.lib.tlog.TLog
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

/**
 * 亲密等级弹框容器组件
 * 用于在聊天页面顶部显示亲密度升级提示
 *
 * 功能特点：
 * - 渐变背景设计
 * - 滑入滑出动画效果
 * - 自动隐藏功能（5秒后）
 * - 点击查看详情功能
 * - 完善的日志记录
 */
@Composable
fun IntimacyLevelCardContainer(
    modifier: Modifier = Modifier,
    friendAvatar: String = "", // 对方头像URL
    userAvatar: String = "", // 用户头像URL
    onCardClick: () -> Unit = {},
    onDismiss: () -> Unit = {}
) {
    TLog.debug(
        "IntimacyLevelCard",
        "IntimacyLevelCardContainer 开始渲染，friendAvatar: $friendAvatar, userAvatar: $userAvatar"
    )

    // 控制弹框显示状态
    var isVisible by remember { mutableStateOf(false) }

    // 启动时显示弹框，3秒后自动隐藏
    LaunchedEffect(Unit) {
        TLog.debug("IntimacyLevelCard", "弹框开始显示动画")
        isVisible = true
        // 3秒后自动隐藏
        delay(4200)
        if (isVisible) {
            TLog.debug("IntimacyLevelCard", "弹框自动隐藏")
            isVisible = false
            delay(600) // 等待动画完成
            onDismiss()
        }
    }

    val scope = rememberCoroutineScope()
    AnimatedVisibility(
        visible = isVisible,
        enter = slideInVertically(
            initialOffsetY = { -it },
            animationSpec = tween(durationMillis = 600)
        ),
        exit = slideOutVertically(
            targetOffsetY = { -it },
            animationSpec = tween(durationMillis = 600)
        ),
        modifier = modifier
    ) {
        IntimacyLevelCard(
            friendAvatar = friendAvatar,
            userAvatar = userAvatar,
            onCardClick = {
                TLog.debug("IntimacyLevelCard", "用户点击弹框")
                isVisible = false

                scope.launch {
                    delay(600) // 等待动画完成
                    onCardClick()
                }
            },
            onCloseClick = {
                TLog.debug("IntimacyLevelCard", "用户关闭弹框")
                isVisible = false

                scope.launch {
                    delay(600) // 等待动画完成
                    onDismiss()
                }
            }
        )
    }
}

/**
 * 亲密等级卡片组件
 */
@Composable
private fun IntimacyLevelCard(
    modifier: Modifier = Modifier,
    friendAvatar: String = "", // 对方头像URL
    userAvatar: String = "", // 用户头像URL
    onCardClick: () -> Unit = {},
    onCloseClick: () -> Unit = {},
) {
    ConstraintLayout(
        modifier = modifier
            .padding(16.dp)
            .shadow(16.dp, shape = RoundedCornerShape(24.dp))
            .fillMaxWidth()
            .noRippleClickable { onCardClick() }
    ) {
        val (star, bg, content, close) = createRefs()

        Image(
            painter = painterResource(R.mipmap.chat_ic_friendliness_level_bg),
            contentDescription = "查看详情",
            contentScale = ContentScale.FillBounds,
            modifier = Modifier
                .constrainAs(bg) {
                    top.linkTo(parent.top)
                    start.linkTo(parent.start)
                    end.linkTo(parent.end)

                    width = Dimension.fillToConstraints
                    height = Dimension.ratio("351:104")
                }
        )

        Image(
            painter = painterResource(R.mipmap.chat_ic_friendliness_level_star),
            contentDescription = "查看详情",
            modifier = Modifier
                .size(24.dp)
                .constrainAs(star) {
                    top.linkTo(parent.top, 6.dp)
                    start.linkTo(parent.start, 56.dp)
                }
        )

        Row(
            modifier = Modifier
                .fillMaxWidth()
                .constrainAs(content) {
                    top.linkTo(bg.top, 24.dp)
                    start.linkTo(bg.start, 20.dp)
                    end.linkTo(close.start, 20.dp)

                    width = Dimension.fillToConstraints
                },
            verticalAlignment = Alignment.CenterVertically
        ) {
            Row(modifier = Modifier) {
                // 对方头像和昵称
                AsyncImage(
                    model = friendAvatar,
                    contentDescription = "对方头像",
                    modifier = Modifier
                        .border(width = 1.dp, color = Color.White, shape = CircleShape)
                        .size(30.dp)
                        .clip(CircleShape),
                    contentScale = ContentScale.Crop,
                    placeholder = painterResource(R.mipmap.common_default_avatar),
                    error = painterResource(R.mipmap.common_default_avatar)
                )

                // 用户头像和昵称
                AsyncImage(
                    model = userAvatar,
                    contentDescription = "用户头像",
                    modifier = Modifier
                        .padding(top = 20.dp)
                        .offset(x = (-16).dp)
                        .border(width = 1.dp, color = Color.White, shape = CircleShape)
                        .size(30.dp)
                        .clip(CircleShape),
                    contentScale = ContentScale.Crop,
                    placeholder = painterResource(R.mipmap.common_default_avatar),
                    error = painterResource(R.mipmap.common_default_avatar)
                )
            }

            Image(
                painter = painterResource(R.mipmap.chat_ic_friendliness_level),
                contentDescription = "路径",
                contentScale = ContentScale.FillBounds,
                modifier = Modifier
                    .aspectRatio(204f / 52)
                    .height(52.dp)
            )
        }

        Image(
            painter = painterResource(R.drawable.common_black_close),
            contentDescription = "关闭",
            modifier = Modifier
                .size(24.dp)
                .constrainAs(close) {
                    top.linkTo(bg.top, 20.dp)
                    end.linkTo(bg.end, 20.dp)
                }
                .noRippleClickable {
                    onCloseClick()
                    TLog.debug("IntimacyLevelCard", "用户点击关闭按钮")
                }
        )
    }
}

/**
 * 预览组件
 */
@Preview
@Composable
private fun IntimacyLevelCardContainerPreview() {
    IntimacyLevelCardContainer(
        friendAvatar = "https://example.com/friend_avatar.jpg", // 示例对方头像
        userAvatar = "https://example.com/user_avatar.jpg" // 示例用户头像
    )
}

/**
 * 预览组件
 */
@Preview(showBackground = true, widthDp = 360)
@Composable
private fun IntimacyLevelCardPreview() {
    IntimacyLevelCard(
        friendAvatar = "https://example.com/friend_avatar.jpg", // 示例对方头像
        userAvatar = "https://example.com/user_avatar.jpg" // 示例用户头像
    )
}
