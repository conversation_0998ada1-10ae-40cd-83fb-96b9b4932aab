package com.kanzhun.marry.chat.activity.friendliness.ui

import androidx.compose.animation.core.LinearEasing
import androidx.compose.animation.core.RepeatMode
import androidx.compose.animation.core.animateFloat
import androidx.compose.animation.core.infiniteRepeatable
import androidx.compose.animation.core.rememberInfiniteTransition
import androidx.compose.animation.core.tween
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Path
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.graphics.drawscope.clipPath
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.techwolf.lib.tlog.TLog
import kotlin.math.PI
import kotlin.math.cos
import kotlin.math.pow
import kotlin.math.sin
import kotlin.random.Random

/**
 * 爱心形状的进度条组件
 *
 * 功能特点：
 * - 爱心形状容器，内部显示纵向进度条
 * - 进度条从底部向顶部填充，呈现水波纹效果
 * - 支持自定义水位高度（progress 值，范围 0.0-1.0）
 * - 支持自定义水和爱心边框的颜色
 * - 水波纹动画效果（两层水波，交替波动）
 * - 气泡动画效果（三个不同大小的气泡从水底随机位置持续上浮）
 */
@Composable
fun HeartProgressIndicator(
    progress: Float, // 0.0-1.0
    waterColor: Color = Color(0xFF4FC3F7), // 默认蓝色水
    heartColor: Color = Color(0xFFE91E63), // 默认粉色爱心边框
    modifier: Modifier = Modifier
) {
    // 参数验证和日志记录
    val clampedProgress = progress.coerceIn(0f, 1f)

    TLog.debug(
        "HeartProgressIndicator",
        "渲染爱心进度条: progress=$clampedProgress, waterColor=$waterColor, heartColor=$heartColor"
    )

    // 水波动画
    val infiniteTransition = rememberInfiniteTransition(label = "water_wave")

    // 第一层水波动画
    val waveOffset1 by infiniteTransition.animateFloat(
        initialValue = 0f,
        targetValue = 2 * PI.toFloat(),
        animationSpec = infiniteRepeatable(
            animation = tween(3000, easing = LinearEasing),
            repeatMode = RepeatMode.Restart
        ),
        label = "wave1"
    )

    // 第二层水波动画（稍微不同的频率）
    val waveOffset2 by infiniteTransition.animateFloat(
        initialValue = 0f,
        targetValue = 2 * PI.toFloat(),
        animationSpec = infiniteRepeatable(
            animation = tween(4000, easing = LinearEasing),
            repeatMode = RepeatMode.Restart
        ),
        label = "wave2"
    )

    // 气泡动画状态
    val bubbles = remember {
        List(3) { index ->
            BubbleState(
                id = index,
                size = when (index) {
                    0 -> 8f
                    1 -> 12f
                    else -> 6f
                },
                speed = Random.nextFloat() * 0.5f + 0.3f
            )
        }
    }

    // 气泡位置动画
    bubbles.forEach { bubble ->
        val animatedY by infiniteTransition.animateFloat(
            initialValue = 1.2f,
            targetValue = -0.3f,
            animationSpec = infiniteRepeatable(
                animation = tween(
                    durationMillis = (4000 / bubble.speed).toInt(),
                    easing = LinearEasing
                ),
                repeatMode = RepeatMode.Restart
            ),
            label = "bubble_${bubble.id}"
        )
        bubble.currentY = animatedY
    }

    Canvas(
        modifier = modifier.size(120.dp)
    ) {
        val canvasWidth = size.width
        val canvasHeight = size.height

        TLog.debug("HeartProgressIndicator", "Canvas尺寸: width=$canvasWidth, height=$canvasHeight")

        // 创建爱心路径
        val heartPath = createHeartPath(canvasWidth, canvasHeight)

        // 边框宽度
        val strokeWidth = 3.dp.toPx()

        // 创建稍微缩小的内部路径，确保水波不会覆盖边框
        val innerHeartPath = createHeartPath(
            canvasWidth - strokeWidth,
            canvasHeight - strokeWidth
        ).apply {
            // 将内部路径向中心偏移
            translate(Offset(strokeWidth / 2f, strokeWidth / 2f))
        }

        // 先裁剪到内部爱心形状绘制水和气泡
        clipPath(innerHeartPath) {
            // 计算水位高度
            val waterLevel = canvasHeight * (1f - clampedProgress)

            // 绘制水波纹
            drawWaterWaves(
                waterLevel = waterLevel,
                waveOffset1 = waveOffset1,
                waveOffset2 = waveOffset2,
                waterColor = waterColor,
                canvasWidth = canvasWidth,
                canvasHeight = canvasHeight
            )

            // 绘制气泡
            drawBubbles(
                bubbles = bubbles,
                waterLevel = waterLevel,
                canvasWidth = canvasWidth,
                canvasHeight = canvasHeight,
                progress = clampedProgress
            )
        }

        // 最后绘制爱心边框，确保边框在最上层
        drawPath(
            path = heartPath,
            color = heartColor,
            style = Stroke(width = strokeWidth)
        )
    }
}

/**
 * 气泡状态数据类
 */
private data class BubbleState(
    val id: Int,
    val size: Float,
    val speed: Float,
    val x: Float = Random.nextFloat(),
    var currentY: Float = 1.2f
)

/**
 * 创建爱心形状路径
 * 使用标准的爱心绘制算法，确保长宽比例为 1:1
 */
@Suppress("unused")
private fun createHeartPath0(width: Float, height: Float): Path {
    TLog.debug("HeartProgressIndicator", "创建爱心路径: 尺寸${width}x${height}")

    // 计算正方形绘制区域，保持1:1比例居中
    val size = minOf(width, height)
    val offsetX = (width - size) / 2f
    val offsetY = (height - size) / 2f

    return Path().apply {
        // 起点：顶部中心
        moveTo(offsetX + size * 0.5f, offsetY + size * 0.4f)

        // 左心弧：从顶部到尖角
        cubicTo(
            offsetX + size * 0.0f, offsetY + size * 0.0f,     // 控制点1：左外侧顶角
            offsetX + size * 0.0f, offsetY + size * 0.7f,    // 控制点2：左中段
            offsetX + size * 0.5f, offsetY + size * 0.9f      // 终点：底部尖端
        )

        // 右心弧：从尖角回顶部
        cubicTo(
            offsetX + size * 1.0f, offsetY + size * 0.7f,    // 控制点1：右中段
            offsetX + size * 1.0f, offsetY + size * 0.0f,     // 控制点2：右外侧顶角
            offsetX + size * 0.5f, offsetY + size * 0.4f     // 终点：顶部中心
        )

        close()
    }
}

/**
 * 创建爱心形状路径
 * 使用心形数学公式: x = 16sin³(t), y = 13cos(t) - 5cos(2t) - 2cos(3t) - cos(4t)
 */
private fun createHeartPath(width: Float, height: Float): Path {
    TLog.debug("HeartProgressIndicator", "创建爱心路径: 尺寸${width}x${height}")

    val size = minOf(width, height)
    val offsetX = (width - size) / 2f
    val offsetY = (height - size) / 2f

    val scale = size / 32f // 缩放因子
    val centerX = offsetX + size * 0.5f
    val centerY = offsetY + size * 0.3f // 稍微上移

    return Path().apply {
        var isFirst = true

        // 使用参数方程绘制心形
        for (i in 0..1080) {
            val t = Math.toRadians(i.toDouble())

            // 心形参数方程
//            val x = 16 * sin(t).pow(3)
//            val y = 13 * cos(t) - 4 * cos(2 * t) - 3 * cos(3 * t) - cos(4 * t)

            val x = 14 * sin(t).pow(3)
            val y = 10 * (-cos(t).pow(3) - cos(t).pow(2) + 2 * cos(t))

            val pointX = centerX + x.toFloat() * scale
            val pointY = centerY - y.toFloat() * scale // Y轴翻转

            if (isFirst) {
                moveTo(pointX, pointY)
                isFirst = false
            } else {
                lineTo(pointX, pointY)
            }
        }

        close()
    }
}

/**
 * 绘制水波纹效果
 * 创建更真实的水波动画，包含主水体和表面波纹
 */
private fun DrawScope.drawWaterWaves(
    waterLevel: Float,
    waveOffset1: Float,
    waveOffset2: Float,
    waterColor: Color,
    canvasWidth: Float,
    canvasHeight: Float
) {
    val waveHeight = 18f // 增大波浪高度，原来是6f
    val waveLength = canvasWidth / 1.2f // 调整波长，让波浪更明显

    TLog.debug(
        "HeartProgressIndicator",
        "绘制水波纹: waterLevel=$waterLevel, waveHeight=$waveHeight"
    )

    // 主水体（静态部分）
    val mainWaterPath = Path()
    mainWaterPath.moveTo(0f, canvasHeight)
    mainWaterPath.lineTo(0f, waterLevel + waveHeight * 1.5f)
    mainWaterPath.lineTo(canvasWidth, waterLevel + waveHeight * 1.5f)
    mainWaterPath.lineTo(canvasWidth, canvasHeight)
    mainWaterPath.close()

    // 绘制主水体
    drawPath(
        path = mainWaterPath,
        color = waterColor.copy(alpha = 0.9f)
    )

    // 第一层水波（主波纹）
    val wavePath1 = Path()
    wavePath1.moveTo(0f, waterLevel + waveHeight * 1.5f)

    for (x in 0..canvasWidth.toInt() step 2) {
        val normalizedX = x / waveLength * 2 * PI
        val y = waterLevel + sin(normalizedX + waveOffset1).toFloat() * waveHeight
        wavePath1.lineTo(x.toFloat(), y)
    }

    wavePath1.lineTo(canvasWidth, waterLevel + waveHeight * 1.5f)
    wavePath1.lineTo(canvasWidth, canvasHeight)
    wavePath1.lineTo(0f, canvasHeight)
    wavePath1.close()

    // 绘制第一层水波
    drawPath(
        path = wavePath1,
        color = waterColor.copy(alpha = 0.9f)
    )

    // 第二层水波（表面细波纹）- 增大振幅
    val wavePath2 = Path()
    wavePath2.moveTo(0f, waterLevel)

    for (x in 0..canvasWidth.toInt() step 2) {
        val normalizedX = x / (waveLength * 0.8f) * 2 * PI
        val y =
            waterLevel + sin(normalizedX + waveOffset2).toFloat() * waveHeight * 0.7f // 增大振幅从0.4f到0.7f
        wavePath2.lineTo(x.toFloat(), y)
    }

    wavePath2.lineTo(canvasWidth, waterLevel)
    wavePath2.lineTo(canvasWidth, waterLevel + waveHeight * 2.5f)
    wavePath2.lineTo(0f, waterLevel + waveHeight * 2.5f)
    wavePath2.close()

    // 绘制第二层水波（表面效果）
    drawPath(
        path = wavePath2,
        color = waterColor.copy(alpha = 0.5f)
    )

    // 添加水面反光效果
    val reflectionGradient = Brush.verticalGradient(
        colors = listOf(
            Color.White.copy(alpha = 0.4f),
            Color.Transparent
        ),
        startY = waterLevel,
        endY = waterLevel + waveHeight * 4
    )

    val reflectionPath = Path()
    reflectionPath.moveTo(0f, waterLevel)

    for (x in 0..canvasWidth.toInt() step 2) {
        val normalizedX = x / waveLength * 2 * PI
        val y =
            waterLevel + sin(normalizedX + waveOffset1 * 0.6f).toFloat() * waveHeight * 0.6f // 增大反光波纹振幅
        reflectionPath.lineTo(x.toFloat(), y)
    }

    reflectionPath.lineTo(canvasWidth, waterLevel + waveHeight * 4)
    reflectionPath.lineTo(0f, waterLevel + waveHeight * 4)
    reflectionPath.close()

    // 绘制反光效果
    drawPath(
        path = reflectionPath,
        brush = reflectionGradient
    )
}

/**
 * 绘制气泡效果
 * 创建真实的气泡动画，包含大小变化和透明度变化
 */
private fun DrawScope.drawBubbles(
    bubbles: List<BubbleState>,
    waterLevel: Float,
    canvasWidth: Float,
    canvasHeight: Float,
    progress: Float
) {
    if (progress <= 0f) return // 没有水时不显示气泡

    TLog.debug("HeartProgressIndicator", "绘制气泡: 气泡数量=${bubbles.size}, 水位=$waterLevel")

    bubbles.forEach { bubble ->
        val bubbleX = bubble.x * canvasWidth
        val bubbleY = bubble.currentY * canvasHeight

        // 只在水中和水面附近绘制气泡
        val isInWater = bubbleY >= waterLevel
        val isNearSurface = bubbleY >= waterLevel - 30f && bubbleY <= waterLevel + 20f

        if (isInWater || isNearSurface) {
            // 计算气泡大小和透明度
            val bubbleSize: Float
            val bubbleAlpha: Float

            when {
                isInWater -> {
                    // 在水中：正常大小
                    bubbleSize = bubble.size
                    bubbleAlpha = 0.7f
                }

                else -> {
                    // 浮出水面：逐渐变小并消失
                    val distanceAboveWater = waterLevel - bubbleY
                    val fadeRatio = (distanceAboveWater / 30f).coerceIn(0f, 1f)
                    bubbleSize = bubble.size * (1f - fadeRatio * 0.8f)
                    bubbleAlpha = 0.7f * (1f - fadeRatio)
                }
            }

            if (bubbleSize > 0f && bubbleAlpha > 0f) {
                // 绘制气泡主体（带渐变效果）
                val bubbleGradient = Brush.radialGradient(
                    colors = listOf(
                        Color.White.copy(alpha = bubbleAlpha * 0.9f),
                        Color.White.copy(alpha = bubbleAlpha * 0.6f),
                        Color.White.copy(alpha = bubbleAlpha * 0.3f)
                    ),
                    center = Offset(bubbleX, bubbleY),
                    radius = bubbleSize
                )

                drawCircle(
                    brush = bubbleGradient,
                    radius = bubbleSize,
                    center = Offset(bubbleX, bubbleY)
                )

                // 绘制气泡边框
                drawCircle(
                    color = Color.White.copy(alpha = bubbleAlpha * 0.4f),
                    radius = bubbleSize,
                    center = Offset(bubbleX, bubbleY),
                    style = Stroke(width = 1f)
                )

                // 绘制气泡高光
                val highlightSize = bubbleSize * 0.25f
                val highlightOffset = bubbleSize * 0.3f
                drawCircle(
                    color = Color.White.copy(alpha = bubbleAlpha),
                    radius = highlightSize,
                    center = Offset(
                        bubbleX - highlightOffset,
                        bubbleY - highlightOffset
                    )
                )

                // 绘制小高光点
                drawCircle(
                    color = Color.White.copy(alpha = bubbleAlpha * 0.8f),
                    radius = highlightSize * 0.4f,
                    center = Offset(
                        bubbleX + highlightOffset * 0.5f,
                        bubbleY - highlightOffset * 0.8f
                    )
                )
            }
        }
    }
}

/**
 * 预览函数 - 不同进度状态
 */
@Preview(showBackground = true)
@Composable
private fun HeartProgressIndicatorPreview() {
    Column(
        modifier = Modifier.padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // 空状态
        HeartProgressIndicator(
            progress = 0f,
            waterColor = Color(0xFF4FC3F7),
            heartColor = Color(0xFFE91E63)
        )

        // 半满状态
        HeartProgressIndicator(
            progress = 0.5f,
            waterColor = Color(0xFF4FC3F7),
            heartColor = Color(0xFFE91E63)
        )

        // 满状态
        HeartProgressIndicator(
            progress = 1f,
            waterColor = Color(0xFF4FC3F7),
            heartColor = Color(0xFFE91E63)
        )
    }
}

/**
 * 高级爱心进度条组件
 * 支持更多自定义选项和配置
 */
@Composable
fun AdvancedHeartProgressIndicator(
    progress: Float,
    waterColor: Color = Color(0xFF4FC3F7),
    heartColor: Color = Color(0xFFE91E63),
    size: androidx.compose.ui.unit.Dp = 120.dp,
    enableBubbles: Boolean = true,
    enableWaves: Boolean = true,
    waveIntensity: Float = 1f, // 波浪强度 0.0-2.0
    modifier: Modifier = Modifier
) {
    val clampedProgress = progress.coerceIn(0f, 1f)
    val clampedWaveIntensity = waveIntensity.coerceIn(0f, 2f)

    TLog.info(
        "AdvancedHeartProgressIndicator",
        "渲染高级爱心进度条: progress=$clampedProgress, size=$size, enableBubbles=$enableBubbles, enableWaves=$enableWaves, waveIntensity=$clampedWaveIntensity"
    )

    // 动画配置
    val infiniteTransition = rememberInfiniteTransition(label = "advanced_heart_animation")

    val waveOffset1 by infiniteTransition.animateFloat(
        initialValue = 0f,
        targetValue = 2 * PI.toFloat(),
        animationSpec = infiniteRepeatable(
            animation = tween(3000, easing = LinearEasing),
            repeatMode = RepeatMode.Restart
        ),
        label = "advanced_wave1"
    )

    val waveOffset2 by infiniteTransition.animateFloat(
        initialValue = 0f,
        targetValue = 2 * PI.toFloat(),
        animationSpec = infiniteRepeatable(
            animation = tween(4500, easing = LinearEasing),
            repeatMode = RepeatMode.Restart
        ),
        label = "advanced_wave2"
    )

    // 气泡系统
    val bubbles = remember {
        if (enableBubbles) {
            List(4) { index ->
                BubbleState(
                    id = index,
                    size = when (index) {
                        0 -> 6f
                        1 -> 10f
                        2 -> 8f
                        else -> 4f
                    },
                    speed = Random.nextFloat() * 0.4f + 0.4f
                )
            }
        } else {
            emptyList()
        }
    }

    // 气泡动画
    bubbles.forEach { bubble ->
        val animatedY by infiniteTransition.animateFloat(
            initialValue = 1.3f,
            targetValue = -0.4f,
            animationSpec = infiniteRepeatable(
                animation = tween(
                    durationMillis = (5000 / bubble.speed).toInt(),
                    easing = LinearEasing
                ),
                repeatMode = RepeatMode.Restart
            ),
            label = "advanced_bubble_${bubble.id}"
        )
        bubble.currentY = animatedY
    }

    Canvas(
        modifier = modifier.size(size)
    ) {
        val canvasWidth = this.size.width
        val canvasHeight = this.size.height

        // 创建爱心路径
        val heartPath = createHeartPath(canvasWidth, canvasHeight)

        // 边框宽度
        val strokeWidth = 3.dp.toPx()

        // 创建稍微缩小的内部路径，确保水波不会覆盖边框
        val innerHeartPath = createHeartPath(
            canvasWidth - strokeWidth,
            canvasHeight - strokeWidth
        ).apply {
            // 将内部路径向中心偏移
            translate(Offset(strokeWidth / 2f, strokeWidth / 2f))
        }

        // 先裁剪到内部爱心形状绘制水和气泡
        clipPath(innerHeartPath) {
            val waterLevel = canvasHeight * (1f - clampedProgress)

            if (enableWaves) {
                // 绘制增强的水波纹
                drawEnhancedWaterWaves(
                    waterLevel = waterLevel,
                    waveOffset1 = waveOffset1,
                    waveOffset2 = waveOffset2,
                    waterColor = waterColor,
                    canvasWidth = canvasWidth,
                    canvasHeight = canvasHeight,
                    intensity = clampedWaveIntensity
                )
            } else {
                // 绘制静态水面
                drawRect(
                    color = waterColor,
                    topLeft = Offset(0f, waterLevel),
                    size = Size(canvasWidth, canvasHeight - waterLevel)
                )
            }

            if (enableBubbles) {
                drawBubbles(
                    bubbles = bubbles,
                    waterLevel = waterLevel,
                    canvasWidth = canvasWidth,
                    canvasHeight = canvasHeight,
                    progress = clampedProgress
                )
            }
        }

        // 最后绘制爱心边框，确保边框在最上层
        drawPath(
            path = heartPath,
            color = heartColor,
            style = Stroke(width = strokeWidth)
        )
    }
}

/**
 * 增强版水波纹绘制
 */
private fun DrawScope.drawEnhancedWaterWaves(
    waterLevel: Float,
    waveOffset1: Float,
    waveOffset2: Float,
    waterColor: Color,
    canvasWidth: Float,
    canvasHeight: Float,
    intensity: Float
) {
    val baseWaveHeight = 12f * intensity // 增大基础波浪高度
    val waveLength = canvasWidth / 1.2f // 调整波长

    // 主水体
    drawRect(
        color = waterColor.copy(alpha = 0.85f),
        topLeft = Offset(0f, waterLevel + baseWaveHeight * 1.5f),
        size = Size(canvasWidth, canvasHeight - waterLevel - baseWaveHeight * 1.5f)
    )

    // 多层波纹效果
    for (layer in 0..2) {
        val layerWaveHeight = baseWaveHeight * (1f - layer * 0.2f) // 减少层间衰减
        val layerOffset = when (layer) {
            0 -> waveOffset1
            1 -> waveOffset2
            else -> waveOffset1 * 0.7f + waveOffset2 * 0.3f
        }
        val layerAlpha = 0.7f - layer * 0.15f // 增加透明度

        val wavePath = Path()
        wavePath.moveTo(0f, waterLevel + layerWaveHeight * 2.5f)

        for (x in 0..canvasWidth.toInt() step 2) {
            val normalizedX = x / waveLength * 2 * PI
            val y = waterLevel + sin(normalizedX + layerOffset).toFloat() * layerWaveHeight
            wavePath.lineTo(x.toFloat(), y)
        }

        wavePath.lineTo(canvasWidth, waterLevel + layerWaveHeight * 2.5f)
        wavePath.lineTo(canvasWidth, canvasHeight)
        wavePath.lineTo(0f, canvasHeight)
        wavePath.close()

        drawPath(
            path = wavePath,
            color = waterColor.copy(alpha = layerAlpha)
        )
    }
}

@Preview(showBackground = true)
@Composable
private fun HeartProgressIndicatorCustomColorsPreview() {
    Row(
        modifier = Modifier.padding(16.dp),
        horizontalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // 自定义颜色组合1
        HeartProgressIndicator(
            progress = 0.7f,
            waterColor = Color(0xFF81C784), // 绿色水
            heartColor = Color(0xFF388E3C) // 深绿色边框
        )

        // 自定义颜色组合2
        HeartProgressIndicator(
            progress = 0.3f,
            waterColor = Color(0xFFFFB74D), // 橙色水
            heartColor = Color(0xFFFF5722) // 红橙色边框
        )
    }
}

@Preview(showBackground = true)
@Composable
private fun AdvancedHeartProgressIndicatorPreview() {
    Column(
        modifier = Modifier.padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Row(
            horizontalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // 标准版本
            AdvancedHeartProgressIndicator(
                progress = 0.6f,
                size = 100.dp
            )

            // 无气泡版本
            AdvancedHeartProgressIndicator(
                progress = 0.8f,
                enableBubbles = false,
                size = 100.dp,
                waterColor = Color(0xFF9C27B0),
                heartColor = Color(0xFF673AB7)
            )
        }

        Row(
            horizontalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // 无波浪版本
            AdvancedHeartProgressIndicator(
                progress = 0.4f,
                enableWaves = false,
                size = 100.dp,
                waterColor = Color(0xFF00BCD4),
                heartColor = Color(0xFF0097A7)
            )

            // 高强度波浪
            AdvancedHeartProgressIndicator(
                progress = 0.9f,
                waveIntensity = 2f,
                size = 100.dp,
                waterColor = Color(0xFFFF9800),
                heartColor = Color(0xFFE65100)
            )
        }
    }
}
