plugins {
    id 'com.android.application'
    id 'WMRouter'
    id 'kotlin-android'
    id 'kotlin-kapt'
}
//apply plugin: 'com.tencent.bugly.tinker-support'

apply from: "$rootDir/gradle/common_library.gradle"
apply from: "./tinker-support.gradle"

android {
    defaultConfig {
        applicationId "${rootProject.ext.applicationId}"
        versionCode rootProject.ext.appVersionCode
        versionName rootProject.ext.appShowVersionName
        buildConfigField("String", "CHANNEL_STRING", "\"${rootProject.ext.channelString}\"")
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"

        javaCompileOptions {
            annotationProcessorOptions {
                arguments = ["room.schemaLocation": "$projectDir/schemas".toString()]
            }
        }
    }

    packagingOptions {
        jniLibs {
            useLegacyPackaging true
            keepDebugSymbols += ['*/arm64-v8a/libdu.so']
            pickFirsts += ['**/libc++_shared.so']
        }
    }

    productFlavors {
        devOnline {      // 开发版-线上
            rootProject.ext.serverConfig = 1
            buildConfigField("int", "CONFIG", "1")
            manifestPlaceholders = [UMENG_CHANNEL_VALUE: "BzlO"]// 线上环境
        }
        devOffline {     // 开发版-线下
            rootProject.ext.serverConfig = 2
            buildConfigField("int", "CONFIG", "2")
            manifestPlaceholders = [UMENG_CHANNEL_VALUE: "BzlO"]// 线下环境
        }
        qa {
            buildConfigField("int", "CONFIG", "3")
            manifestPlaceholders = [UMENG_CHANNEL_VALUE: "BzlO"]
            isDefault true
        }
        rd {
            buildConfigField("int", "CONFIG", "4")
            manifestPlaceholders = [UMENG_CHANNEL_VALUE: "BzlO"]
        }
        prod { // 发布版
            rootProject.ext.isPrintLog = false
            rootProject.ext.serverConfig = 1
            buildConfigField("boolean", "PRINT_LOG", "false")               // 关闭输出LOG
            buildConfigField("int", "CONFIG", "1")
            manifestPlaceholders = [UMENG_CHANNEL_VALUE: "BzlO"]// 线上环境
        }

        flavorDimensions "default"

//        splits {
//            abi {
//                reset()
//                enable true
//                universalApk false
//                include 'armeabi-v7a', 'arm64-v8a'
//            }
//        }

//        packagingOptions {
//            pickFirst '**/*.so'
//        }
    }

    splits {
        abi {
            reset()
            enable true
            universalApk false
            include 'armeabi-v7a', 'arm64-v8a'
        }

        packagingOptions {
            pickFirst '**/*.so'
        }
    }

    applicationVariants.all { variant ->
        variant.outputs.all { output ->
            output.getProcessManifestProvider().get().doLast {
                // create abi_info_text
                String abi_info_text = "${variant.buildType.name.capitalize()}-${output.name.toLowerCase()}-${defaultConfig.versionName}-${defaultConfig.versionCode}"
                println "abi_info_text=" + abi_info_text

                //agp 3.5用这个
                // File manifestDir = new File(manifestOutputDirectory.get().asFile,output.dirName)
                //agp 7.0+用这个
                File manifestDir = new File(multiApkManifestOutputDirectory.get().asFile, output.dirName)
                String manifestPath = new File(manifestDir, "AndroidManifest.xml").getAbsolutePath()
                println "manifestPath=" + manifestPath
                File target = new File(manifestPath)
                if (!target.exists() || !target.isFile()) {
                    return
                }
                // replace magic text with abi_info_text
                def manifestContent = file(manifestPath).getText()
                manifestContent = manifestContent.replace('android:value="ABI_INFO_MAGIC_PLACE_HOLDER"',
                        String.format('android:value="%s"', abi_info_text))
                // Overwrites the manifest with the new text.
                file(manifestPath).write(manifestContent)
            }
        }
    }

    configurations {
        cleanedAnnotations
        implementation.exclude group: 'org.jetbrains', module: 'annotations'
    }


    signingConfigs {
        release {
            storeFile file("keystore.jks")
            keyAlias "marry"
            keyPassword "techwolf2023"
            storePassword "techwolf2023"
        }
    }
    namespace 'com.kanzhun.marry'
    lint {
        checkDependencies true
    }

    buildTypes {
        debug {
            zipAlignEnabled true
            minifyEnabled project.hasProperty('minifyEnable') ? Boolean.parseBoolean(String.valueOf(project.property('minifyEnable'))) : true
            // 混淆关闭
            shrinkResources project.hasProperty('minifyEnable') ? Boolean.parseBoolean(String.valueOf(project.property('minifyEnable'))) : true
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            signingConfig signingConfigs.release
        }

        release {
            zipAlignEnabled true
            shrinkResources true
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            signingConfig signingConfigs.release
        }
    }
}

dependencies {

    testImplementation 'junit:junit:4.+'
    androidTestImplementation 'androidx.test.ext:junit:1.1.2'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.3.0'
    api project(":lib_foundation")
    api project(":lib_keyboard")
    implementation project(path: ':lib_imageviewer')

    // (Java only)
//    implementation "androidx.work:work-runtime:$work_version"
//
//    // optional - RxJava2 support
//    implementation "androidx.work:work-rxjava3:$work_version"

//    // optional - GCMNetworkManager support
//    implementation "androidx.work:work-gcm:$work_version"
//
//    // optional - Multiprocess support
//    implementation "androidx.work:work-multiprocess:$work_version"
//
//    implementation 'com.google.guava:guava:27.0.1-android'
//
//    //Retrofit test
    implementation 'com.trello.rxlifecycle4:rxlifecycle:4.0.2'

    //AlphaPlayer test
//    implementation 'com.google.android.exoplayer:exoplayer:2.14.1'
//    implementation 'com.github.bytedance:AlphaPlayer:1.0.6'

    kapt deps.wmrouter_compiler
    //数盟
    api 'com.twl.sdk.file:shumeng-so:8.3.9'
    api 'com.twl.sdk.file:shumeng-du:8.3.9'
    implementation project(':lib_startup')

    implementation project(":module_me")
    implementation project(":lib_webview")
    implementation project(":module_activity")
    implementation project(":module_matching")
    implementation project(":module_chat")
    implementation project(":module_video_meeting")
    implementation project(":lib_push")
    implementation project(":moudle_social")
    implementation project(":module_parent")
//    implementation project(":module_new_task")
    implementation project(":lib_share")
//    implementation project(":lib_kuikly")

    //开发工具包
//    debugImplementation 'com.twl.shop.manager.zhipin:module-kit:o_9.4.8-beta'
//    releaseImplementation 'com.twl.shop.manager.zhipin:lib-kit:0.1.0'

    //bugly

    //android14 debug卡顿
    debugImplementation "com.bzl.lib:android14-debug-opt:0.0.1-beta2"
//    debugImplementation(deps.apm.settings)

    debugImplementation libs.uetool
    debugImplementation libs.uetool.base
    releaseImplementation libs.uetool.no.op

    implementation libs.room.ktx
    implementation libs.room.runtime
    implementation libs.koin.core
    implementation libs.koin.android
    kapt libs.room.compiler
}

apply plugin: 'com.bzl.oneapk.compress'

ImageConfig {
    logLevel "DEBUG" //插件日志打印级别，枚举值包括VERBOSE、DEBUG、INFO、WARN、ERROR
    optimizeType "AutoCompress" //优化类型，可选"ConvertWebp"、"CompressImage"、"AutoCompress" 三种模式压缩，默认AutoCompress
    maxSize 100 * 1024 //大图片阈值，default 100KB
    isCheckSize true //是否检测图片大小，默认为true
    isCheckPixels true // 是否检测大像素图片，default true
    maxWidth 500 //default 1000 如果开启图片宽高检查，默认的最大宽度
    maxHeight 500 //default 1000 如果开启图片宽高检查，默认的最大高度
    isSupportAlphaWebp true  //是否支持带有透明度的webp，default true, 带有透明图的图片会进行压缩
    multiThread true  //是否开启多线程处理图片，default true
    whiteList = [ // 默认为空，如果添加，对图片不进行任何处理
            // module_matching
                  "matching_bg_well_selected_lucky_egg_card.webp",
                  "matching_bg_recommend_newcomer.png",
                  "matching_bg_lovers.png",
                  "matching_bg_recommend_like.png",
                  "matching_bg_recommend_finish.png",
                  "matching_destiny.png",
                  "matching_empathy.png",
                  "matching_bg_top_well_selected.png",
    ]
    bigImageWhiteList = [ //默认为空，如果添加，大图检测将跳过这些图片
            // module_matching
                          "matching_bg_lovers.png",
                          "matching_bg_empathy_instruct.png",
                          "matching_bg_well_selected_info_card.png",
                          "matching_bg_mood.png",
                          "matching_bg_like_each_other.png",
                          "matching_bg_small_avatar_well_selected.png",
    ]
    isolateList = [ //配置后开启孤立模式，只压缩配置中的图片

    ]
    gitCommitMode true // default true 是否是git提交前自动检查压缩模式
}