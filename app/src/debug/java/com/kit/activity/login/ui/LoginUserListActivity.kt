package com.kit.activity.login.ui

import android.content.Intent
import android.os.Bundle
import android.widget.Toast
import androidx.activity.viewModels
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material.icons.outlined.Delete
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.Card
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.material3.TopAppBar
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.core.os.bundleOf
import coil.compose.AsyncImage
import com.hpbr.apm.common.utils.AsyncThreadTask
import com.kanzhun.common.base.compose.BaseComposeActivity
import com.kanzhun.common.base.compose.ui.O2Button
import com.kanzhun.common.kotlin.ui.statusbar.darkMode
import com.kanzhun.common.kotlin.ui.statusbar.statusBarColor
import com.kanzhun.common.util.AppUtil
import com.kanzhun.foundation.kernel.account.AccountHelper
import com.kanzhun.foundation.router.LoginPageRouter
import com.kanzhun.foundation.utils.LogoutUtil
import com.kanzhun.marry.login.fragment.login.LoginInputNumberFragment.KEY_QA_PHONE
import com.kanzhun.utils.T
import com.kit.activity.login.data.entity.LoginUserEntity
import com.kit.activity.login.util.AutoLoginHelper
import com.techwolf.lib.tlog.TLog
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

class LoginUserListActivity : BaseComposeActivity() {
    private val viewModel: LoginUserViewModel by viewModels(factoryProducer = {
        LoginUserViewModelFactory(
            application
        )
    })

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        // 设置黑色状态栏，与 KitMainActivity 保持一致
        statusBarColor(android.graphics.Color.BLACK)
        darkMode(false) // false 表示白色文字
    }

    @Composable
    override fun OnSetContent() {
        val users by viewModel.allUsers.collectAsState(initial = emptyList())

        var showClearAllDialog by remember { mutableStateOf(false) }

        val isLoggedIn = AccountHelper.getInstance().isLogin

        LoginList(
            isLoggedIn = isLoggedIn,
            users = users,
            onBack = { AppUtil.finishActivity(this@LoginUserListActivity) },
            onLogin = { user ->
                // 实现真正的自动登录
                TLog.info("LoginUserListActivity", "=== 用户点击自动登录 ===")
                TLog.info("LoginUserListActivity", "用户信息: phone=${user.phone}, nickname=${user.nickname}")
                TLog.info("LoginUserListActivity", "当前登录状态: ${AccountHelper.getInstance().isLogin}")

                if (AccountHelper.getInstance().isLogin) {
                    // 如果当前已登录，先退出登录，再执行自动登录
                    TLog.info("LoginUserListActivity", "当前已登录，先执行退出登录...")
                    viewModel.logout(onSuccess = {
                        TLog.info("LoginUserListActivity", "退出登录成功，400ms后执行自动登录")
                        AsyncThreadTask.executeDelayedToUI({
                            performAutoLogin(user)
                        }, 400)
                    })
                } else {
                    // 直接执行自动登录
                    TLog.info("LoginUserListActivity", "当前未登录，直接执行自动登录")
                    performAutoLogin(user)
                }
            },
            onLogout = {
                // 检查是否有登录用户和当前登录状态
                val isLoggedIn = AccountHelper.getInstance().isLogin
                val hasUsers = users.isNotEmpty()

                if (!isLoggedIn && !hasUsers) {
                    // 没有登录用户且当前未登录
                    TLog.info("LoginUserListActivity", "当前没有已登录用户，无法退出登录")
                    T.ss("当前没有已登录用户，请登录")
                } else if (!isLoggedIn) {
                    // 有历史用户但当前未登录
                    TLog.info("LoginUserListActivity", "当前未登录状态，无法退出登录")
                    T.ss("当前没有已登录用户，请登录")
                } else {
                    // 当前已登录，执行退出登录
                    TLog.info("LoginUserListActivity", "执行退出登录操作")
                    LogoutUtil.logoutDialog(this@LoginUserListActivity)
                }
            },
            onClearAll = {
                showClearAllDialog = true
            },
            onDeleteUser = { user ->
                viewModel.deleteUser(user)
                Toast.makeText(
                    this@LoginUserListActivity,
                    "已删除用户",
                    Toast.LENGTH_SHORT
                ).show()
            }
        )

        if (showClearAllDialog) {
            val onDismiss = { showClearAllDialog = false }

            CustomAlertDialog(
                dialogTitle = "清除所有登录账号吗？",
                dialogText = "",
                onConfirmation = {
                    viewModel.deleteAllUsers()
                    Toast.makeText(
                        this@LoginUserListActivity,
                        "已清除所有账号",
                        Toast.LENGTH_SHORT
                    ).show()

                    onDismiss()
                },
                onDismissRequest = onDismiss
            )
        }
    }

    private fun jumpToLoginPage(user: LoginUserEntity) {
        TLog.info("LoginUserListActivity", "=== 跳转到登录页面 ===")
        TLog.info("LoginUserListActivity", "预填手机号: ${user.phone}")

        val req = AppUtil.getDefaultUriRequest(
            this,
            LoginPageRouter.APP_LOGIN_ACTIVITY
        )
        req.setIntentFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
        req.putExtras(bundleOf().apply {
            putString(KEY_QA_PHONE, user.phone)
        })
        req.start()

        TLog.info("LoginUserListActivity", "登录页面跳转完成")
    }

    /**
     * 执行自动登录
     */
    private fun performAutoLogin(user: LoginUserEntity) {
        TLog.info("LoginUserListActivity", "=== 开始执行自动登录 ===")
        TLog.info("LoginUserListActivity", "用户信息: phone=${user.phone}, userId=${user.userId}")

        AutoLoginHelper.performAutoLogin(
            phone = user.phone,
            onSuccess = { loginModel ->
                TLog.info("LoginUserListActivity", "=== 自动登录成功回调 ===")
                TLog.info("LoginUserListActivity", "登录结果: userId=${loginModel.userId ?: "null"}, nickName=${loginModel.nickName ?: "null"}")
                T.ss("自动登录成功")

                try {
                    // 更新用户最后登录时间
                    TLog.info("LoginUserListActivity", "更新用户最后登录时间...")
                    val updatedUser = LoginUserEntity(
                        user.userId,
                        user.avatar,
                        user.nickname,
                        user.phone,
                        user.gender,
                        System.currentTimeMillis()
                    )
                    viewModel.insertOrUpdateUser(updatedUser)
                    TLog.info("LoginUserListActivity", "用户信息更新完成")

                    // 立即关闭当前页面，避免与 LoginResultRouter 的页面管理冲突
                    TLog.info("LoginUserListActivity", "立即关闭当前页面...")
                    AppUtil.finishActivity(this@LoginUserListActivity)
                    TLog.info("LoginUserListActivity", "=== LoginUserListActivity 处理完成 ===")
                } catch (e: Exception) {
                    TLog.error("LoginUserListActivity", "自动登录成功后处理异常: ${e.message}", e)
                }
            },
            onFail = { errorReason ->
                TLog.error("LoginUserListActivity", "=== 自动登录失败回调 ===")
                TLog.error("LoginUserListActivity", "错误码: ${errorReason.errCode ?: "null"}")
                TLog.error("LoginUserListActivity", "错误信息: ${errorReason.errReason ?: "null"}")
                T.ss("自动登录失败: ${errorReason.errReason ?: "未知错误"}")

                // 自动登录失败，回退到原来的跳转登录页面逻辑
                TLog.info("LoginUserListActivity", "回退到手动登录页面...")
                jumpToLoginPage(user)
                TLog.info("LoginUserListActivity", "=== 自动登录失败处理完成 ===")
            }
        )
    }
}

@Composable
private fun CustomAlertDialog(
    onDismissRequest: () -> Unit = {},
    onConfirmation: () -> Unit = {},
    dialogTitle: String = "",
    dialogText: String = "",
) {
    AlertDialog(
        title = {
            Text(text = dialogTitle)
        },
        text = {
            Text(text = dialogText)
        },
        onDismissRequest = {
            onDismissRequest()
        },
        confirmButton = {
            TextButton(
                onClick = {
                    onConfirmation()
                }
            ) {
                Text("确定")
            }
        },
        dismissButton = {
            TextButton(
                onClick = {
                    onDismissRequest()
                }
            ) {
                Text("取消")
            }
        }
    )
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun LoginList(
    isLoggedIn: Boolean = false,
    users: List<LoginUserEntity>,
    onBack: () -> Unit = {},
    onLogin: (LoginUserEntity) -> Unit = {},
    onLogout: () -> Unit = {},
    onClearAll: () -> Unit = {},
    onDeleteUser: (LoginUserEntity) -> Unit = {}
) {
    // 检查登录状态
    val canLogout = isLoggedIn
    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("登录账号列表") },
                navigationIcon = {
                    IconButton(onClick = onBack) {
                        Icon(
                            imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                            contentDescription = "返回"
                        )
                    }
                },
                actions = {
                    if (users.isNotEmpty()) {
                        IconButton(onClick = onClearAll) {
                            Icon(
                                imageVector = Icons.Outlined.Delete,
                                contentDescription = "清除所有",
                            )
                        }
                    }
                }
            )
        },
        bottomBar = {
            O2Button(
                text = "退出登录",
                onClick = onLogout,
                modifier = Modifier.padding(16.dp),
                enabled = canLogout
            )
        }
    ) { paddingValues ->
        if (users.isNotEmpty()) {
            LazyColumn(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(paddingValues),
                contentPadding = PaddingValues(bottom = 32.dp, top = 8.dp)
            ) {
                items(users) { user ->
                    UserItem(
                        user = user,
                        onUserClick = {
                            onLogin(user)
                        },
                        onDeleteClick = {
                            onDeleteUser(user)
                        }
                    )
                }
            }
        } else {
            Box(modifier = Modifier.fillMaxSize(), contentAlignment = Alignment.Center) {
                Text(
                    text = "没有登录账号",
                    style = MaterialTheme.typography.titleSmall.copy(color = Color.LightGray),
                    modifier = Modifier
                )
            }
        }
    }
}

@Composable
private fun UserItem(
    user: LoginUserEntity,
    onUserClick: () -> Unit,
    onDeleteClick: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp, vertical = 8.dp)
            .clickable(onClick = onUserClick),
        shape = RoundedCornerShape(0.dp),
    ) {
        Row(
            modifier = Modifier
                .padding(16.dp)
                .fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            AsyncImage(
                model = user.avatar,
                contentDescription = "头像",
                modifier = Modifier
                    .size(56.dp)
                    .background(Color.Gray)
            )

            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = user.nickname,
                    style = MaterialTheme.typography.titleLarge,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )
                Spacer(modifier = Modifier.height(4.dp))
                Text(
                    text = user.phone,
                    style = MaterialTheme.typography.bodyMedium,
                )
                Spacer(modifier = Modifier.height(4.dp))
                Text(
                    text = "上次登录: ${formatDate(user.lastLoginTime)}",
                    style = MaterialTheme.typography.labelSmall,
                )
            }

            IconButton(onClick = onDeleteClick) {
                Icon(
                    imageVector = Icons.Default.Delete,
                    contentDescription = "删除",
                )
            }
        }
    }
}

private fun formatDate(timestamp: Long): String {
    return SimpleDateFormat("yyyy-MM-dd HH:mm", Locale.getDefault()).format(Date(timestamp))
}

@Preview
@Composable
private fun LoginListPreview() {
    LoginList(
        isLoggedIn = true,
        users = listOf(
            LoginUserEntity(
                /* userId = */ "1",
                /* avatar = */ "1234567890",
                /* nickname = */ "测试用户",
                /* phone = */ "18678909876",
                /* gender = */ 1,
                /* lastLoginTime = */ System.currentTimeMillis()
            ),
            LoginUserEntity(
                /* userId = */ "2",
                /* avatar = */ "0987654321",
                /* nickname = */ "另一个用户",
                /* phone = */ "18678999876",
                /* gender = */ 0,
                /* lastLoginTime = */ System.currentTimeMillis()
            ),
            LoginUserEntity(
                /* userId = */ "3",
                /* avatar = */ "1234567890",
                /* nickname = */ "测试用户",
                /* phone = */ "18678909806",
                /* gender = */ 1,
                /* lastLoginTime = */ System.currentTimeMillis()
            ),
            LoginUserEntity(
                /* userId = */ "4",
                /* avatar = */ "0987654321",
                /* nickname = */ "另一个用户",
                /* phone = */ "13678909806",
                /* gender = */ 0,
                /* lastLoginTime = */ System.currentTimeMillis()
            ),
            LoginUserEntity(
                /* userId = */ "5",
                /* avatar = */ "1234567890",
                /* nickname = */ "测试用户",
                /* phone = */ "18674902876",
                /* gender = */ 1,
                /* lastLoginTime = */ System.currentTimeMillis()
            ),
            LoginUserEntity(
                /* userId = */ "6",
                /* avatar = */ "0987654321",
                /* nickname = */ "另一个用户",
                /* phone = */ "18674902866",
                /* gender = */ 0,
                /* lastLoginTime = */ System.currentTimeMillis()
            ),
            LoginUserEntity(
                /* userId = */ "7",
                /* avatar = */ "1234567890",
                /* nickname = */ "测试用户",
                /* phone = */ "18674902076",
                /* gender = */ 1,
                /* lastLoginTime = */ System.currentTimeMillis()
            ),
        ).take(7)
    )
}

@Preview
@Composable
private fun LoginListEmptyPreview() {
    LoginList(users = emptyList())
}

@Preview
@Composable
private fun CustomAlertDialogPreview() {
    CustomAlertDialog(
        dialogTitle = "清除所有账号",
        dialogText = "确定要清除所有登录账号吗？",
    )
}
