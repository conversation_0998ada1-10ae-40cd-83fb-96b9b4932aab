package com.kanzhun.marry.login.api;

import android.util.ArrayMap;

import com.kanzhun.foundation.api.base.URLConfig;
import com.kanzhun.foundation.api.model.CommonAddressResponse;
import com.kanzhun.foundation.bean.SMSLoginModel;
import com.kanzhun.foundation.bean.UpdateUserInfoResponse;
import com.kanzhun.foundation.model.GeeCaptchaRegisterResponse;
import com.kanzhun.http.response.BaseResponse;
import com.kanzhun.marry.login.api.model.AccountStatusResponse;
import com.kanzhun.marry.login.api.model.AddMarryIntentBean;
import com.kanzhun.foundation.bean.LikeEachOtherListBean;
import com.kanzhun.marry.login.api.model.MarryIntentBean;
import com.kanzhun.foundation.api.response.OrangeDicProfileTagResponse;
import com.kanzhun.marry.login.api.model.SubmitMarryIntentBean;
import com.kanzhun.marry.login.bean.PublicConfigBean;
import com.kanzhun.marry.me.api.model.UserCertListModelK;

import java.util.Map;

import io.reactivex.rxjava3.annotations.NonNull;
import io.reactivex.rxjava3.core.Observable;
import retrofit2.http.FieldMap;
import retrofit2.http.FormUrlEncoded;
import retrofit2.http.GET;
import retrofit2.http.POST;
import retrofit2.http.Query;

/**
 * Created by ChaiJiangpeng
 * Date: 2022/3/4
 * <p>
 * "post使用@Field @FieldMap @FormUrlEncoded  注解配合使用"
 * "get使用@Query @QueryMap  注解配合使用"
 */
public interface LoginApi {

    @GET(URLConfig.URL_REGISTER_ACCOUNT_STATUS)
    Observable<BaseResponse<AccountStatusResponse>> checkAccountStatus(@Query("regionCode") String regionCode, @Query("phone") String phone);

    @GET(URLConfig.URL_ORANGE_DIC_PROFILE_TAG)
    Observable<BaseResponse<OrangeDicProfileTagResponse>> dicProfileTag(@Query("scene") String regionCode); // 场景 0:标签 1:爱好 具体见备注

    @GET(URLConfig.URL_ORANGE_LIKE_EACH_OTHER_LIST)
    Observable<BaseResponse<LikeEachOtherListBean>> likeEachOtherList();

    @FormUrlEncoded
    @POST(URLConfig.URL_SMS_LOGIN)
    Observable<BaseResponse<SMSLoginModel>> smsLogin(@FieldMap ArrayMap<String, Object> map);

    @GET(URLConfig.URL_AUTO_LOGIN)
    Observable<BaseResponse<SMSLoginModel>> autoLogin(@Query("phone") String phone);

    @FormUrlEncoded
    @POST(URLConfig.URL_GEE_CAPTCHA_REGISTER)
    Observable<BaseResponse<GeeCaptchaRegisterResponse>> geeCaptchaRegister(@FieldMap ArrayMap<String, Object> map);

    /**
     * @param model 1-直辖市下级只返回区，2-直辖市下级只返回市
     */
    @GET(URLConfig.URL_COMMON_ADDRESS)
    Observable<BaseResponse<CommonAddressResponse>> getCommonAddress(@Query("model") int model);

    @FormUrlEncoded
    @POST(URLConfig.URL_REGISTER_ADD_INFO)
    Observable<BaseResponse<UpdateUserInfoResponse>> updateUserInfo(@FieldMap Map<@NonNull String, Object> map);

    @GET(URLConfig.URL_USER_CERT_LIST)
    Observable<BaseResponse<UserCertListModelK>> queryUserCertListJ();

    @FormUrlEncoded
    @POST(URLConfig.URL_AUTH_CHANGE_ACCOUNT_TYPE)
    Observable<BaseResponse<SMSLoginModel>> changeAccountModel(@FieldMap ArrayMap<String, Object> map);

    @FormUrlEncoded
    @POST(URLConfig.URL_WE_CHAT_LOGIN)
    Observable<BaseResponse<SMSLoginModel>> wechatLogin(@FieldMap ArrayMap<String, Object> map);

    @GET(URLConfig.URL_GET_MARRY_INTENT)
    Observable<BaseResponse<MarryIntentBean>> getMarryIntent();

    @FormUrlEncoded
    @POST(URLConfig.URL_SUBMIT_MARRY_INTENT)
    Observable<BaseResponse<SubmitMarryIntentBean>> submitMarryIntent(@FieldMap ArrayMap<String, Object> map);

    @FormUrlEncoded
    @POST(URLConfig.URL_ADD_MARRY_INTENT)
    Observable<BaseResponse<AddMarryIntentBean>> addMarryIntent(@FieldMap ArrayMap<String, Object> map);

    @GET(URLConfig.URL_PUBLIC_CONFIG)
    Observable<BaseResponse<PublicConfigBean>> queryPublicConfig();
}
