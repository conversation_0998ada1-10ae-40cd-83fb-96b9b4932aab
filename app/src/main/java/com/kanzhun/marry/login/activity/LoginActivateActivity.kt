package com.kanzhun.marry.login.activity

import android.content.Intent
import androidx.annotation.NavigationRes
import androidx.lifecycle.ViewModelProvider
import androidx.navigation.NavController
import androidx.navigation.NavGraph
import androidx.navigation.findNavController
import com.gyf.immersionbar.ImmersionBar.getStatusBarHeight
import com.gyf.immersionbar.ktx.immersionBar
import com.kanzhun.common.constract.BundleConstants
import com.kanzhun.common.kotlin.ext.liveEventBusObserve
import com.kanzhun.common.util.AppUtil
import com.kanzhun.foundation.account.getPlanFragmentList
import com.kanzhun.foundation.account.initPosition
import com.kanzhun.foundation.account.isPlanA
import com.kanzhun.foundation.account.isPlanB
import com.kanzhun.foundation.account.isPlanC
import com.kanzhun.foundation.account.planPosition
import com.kanzhun.foundation.base.activity.BaseBindingActivity
import com.kanzhun.foundation.isQaDebugUser
import com.kanzhun.foundation.router.LoginPageRouter
import com.kanzhun.foundation.router.MePageRouter.TYPE_EDIT_INTRO
import com.kanzhun.marry.R
import com.kanzhun.marry.databinding.ActivityLoginActivateBinding
import com.kanzhun.marry.login.viewmodel.LoginActivateViewModel
import com.kanzhun.marry.module_new_task.viewmodel.NewUserTaskNewViewModel
import com.sankuai.waimai.router.annotation.RouterUri

@RouterUri(path = [LoginPageRouter.LOGIN_ACTIVATE_ACTIVITY])
class LoginActivateActivity :
    BaseBindingActivity<ActivityLoginActivateBinding, LoginActivateViewModel>() {

    override fun preInit(intent: Intent) {

        mViewModel.type = intent.getIntExtra(BundleConstants.BUNDLE_DATA_INT, 0)
        if (mViewModel.type == TYPE_EDIT_INTRO) { // 复用自我介绍（编辑流程）
            val activityViewModel: NewUserTaskNewViewModel by lazy {
                ViewModelProvider(this)[NewUserTaskNewViewModel::class.java]
            }
            activityViewModel.type = TYPE_EDIT_INTRO
            activityViewModel.intent = intent

            val navController: NavController = findNavController(R.id.fragment)
            val navGraph: NavGraph =
                navController.navInflater.inflate(R.navigation.login_activate_navigation)
            navGraph.setStartDestination(R.id.introductionFragment)
            navController.graph = navGraph
        } else if (mViewModel.type == 2 && (isPlanC() || isPlanA())) {
            val activityViewModel: NewUserTaskNewViewModel by lazy {
                ViewModelProvider(this)[NewUserTaskNewViewModel::class.java]
            }
            activityViewModel.type = 2

            //是来选择兴趣爱好标签的（编辑流程）
            val navController: NavController = findNavController(R.id.fragment)
            val navGraph: NavGraph =
                navController.navInflater.inflate(R.navigation.login_activate_navigation)
            navGraph.setStartDestination(R.id.interestFragment)
            navController.graph = navGraph

            val array = intent.getIntegerArrayListExtra(BundleConstants.BUNDLE_DATA_INT_ARRAY)
            array?.forEach {
                activityViewModel.array.add(it)
            }
        } else if (mViewModel.type == 1 || mViewModel.type == 2) {
            //是来选择个性/兴趣标签的（编辑流程）
            val navController: NavController = findNavController(R.id.fragment)
            val navGraph: NavGraph =
                navController.navInflater.inflate(R.navigation.login_activate_navigation)
            navGraph.setStartDestination(R.id.keywordFragment)
            navController.graph = navGraph

            val array = intent.getIntegerArrayListExtra(BundleConstants.BUNDLE_DATA_INT_ARRAY)
            array?.forEach {
                mViewModel.array.add(it)
            }
        } else {
            //是来首善的
            mViewModel.initStatus.observe(this) {
                if (it == 1) {
                    mViewModel.showContent()
                    if (isPlanA()) {
                        val tmp = ViewModelProvider(this)[NewUserTaskNewViewModel::class.java]
                        tmp.initResponseLiveData.observe(this@LoginActivateActivity) {
                            if (it) {
                                planAFirstPage()
                                setupNavigationWithDynamicStart(
                                    R.navigation.login_activate_navigation_a,
                                    mViewModel.targetStartFragmentId
                                )
                            } else {
                                mViewModel.showError()
                            }
                        }
                    } else if (isPlanB()) {

                        val tmp = ViewModelProvider(this)[NewUserTaskNewViewModel::class.java]
                        tmp.initResponseLiveData.observe(this@LoginActivateActivity) {
                            if (it) {
                                planBFirstPage()
                                setupNavigationWithDynamicStart(
                                    R.navigation.login_activate_navigation_b,
                                    mViewModel.targetStartFragmentId
                                )
                            } else {
                                mViewModel.showError()
                            }
                        }
                    } else {
                        planCFirstPage()
                        setupNavigationWithDynamicStart(
                            R.navigation.login_activate_navigation,
                            mViewModel.targetStartFragmentId
                        )
                    }
                } else if (it == 2) {
                    mViewModel.showError()
                }
            }
            mViewModel.showLoading()
            mViewModel.getData()
        }
        liveEventBusObserve("other_platform_complete") { it: Boolean ->
            gotoSkip()
        }
    }

    // 新增：设置带有动态起始点的navigation
    private fun setupNavigationWithDynamicStart(@NavigationRes graphResId: Int, startDestId: Int) {
        val navController: NavController = findNavController(R.id.fragment)
        val navGraph: NavGraph = navController.navInflater.inflate(graphResId)

        // 使用ViewModel中设置的起始fragment
        navGraph.setStartDestination(startDestId)
        navController.graph = navGraph

        // 通知ViewModel navigation已经准备好
        mViewModel.navigationReadyLiveData.postValue(true)
    }

    override fun initView() {
        initObserver()

        immersionBar {
            statusBarColor(android.R.color.white)
            statusBarDarkFont(true)
        }

       val lp =  mBinding.idBar.layoutParams
        lp.height = getStatusBarHeight(this)
        mBinding.idBar.layoutParams = lp
    }

    private fun initObserver() {
        mViewModel.activateSuccessLivaData.observe(this) {
            if (it == -1) {
                gotoSkip()
            }
        }
    }

    private fun gotoSkip() {
        LoginPageRouter.jumpToSkipActivity(this@LoginActivateActivity, false)
        AppUtil.finishActivityDelay(this@LoginActivateActivity)
    }

    override fun initData() {

    }

    override fun onRetry() {
        mViewModel.getData()
    }

    override fun getStateLayout() = mBinding.idStateLayout

    private fun planAFirstPage() {
        mViewModel.userInfoLiveData.value.let {
            if (it?.baseInfo?.nickName.isNullOrEmpty()) {//姓名
                planPosition = 0
            } else if (it.baseInfo?.gender == 0) {//性别
                planPosition = 1
            } else if (it.baseInfo?.birthday.isNullOrEmpty()) {//生日
                planPosition = 2
            } else if (it.baseInfo?.addressCode.isNullOrEmpty()) {//现居地
                planPosition = 3
            } else if ((it.baseInfo?.degree ?: 0) < 10) {//学校
                planPosition = 4
            } else if (it.baseInfo?.loveGoal == 0) {//恋爱目标
                planPosition = 5
            } else if (it.baseInfo?.height == 0) {//身高
                planPosition = 6
            } else if (it.baseInfo?.hukouCode.isNullOrEmpty()) {//户口
                planPosition = 7
            } else if (it.baseInfo?.hometownCode.isNullOrEmpty()) {//家乡
                planPosition = 8
            } else if (it.baseInfo?.industryCode.isNullOrEmpty()) {//行业、职业
                planPosition = 9
            } else if (it.baseInfo?.companyName.isNullOrEmpty()) {//公司
                planPosition = 11
            } else if (it.baseInfo?.carHold == 0 || it.baseInfo?.houseHold == 0) {//资产
                planPosition = 12
            } else if (it.profileTagList.isEmpty()) {//个性标签
                planPosition = 13
            } else if (it.interestTagList.isEmpty()) {//兴趣标签
                planPosition = 14
            } else if (it.baseInfo?.intro.isNullOrEmpty()) {//简介
                planPosition = 15
            } else {//生活照
                planPosition = 16
            }
        }
        if (isQaDebugUser()) {
            planPosition = 15
        }
        initPosition = planPosition
        mViewModel.targetStartFragmentId = getPlanFragmentList()[planPosition]
    }

    private fun planBFirstPage() {
        mViewModel.userInfoLiveData.value.let {
            if (it?.baseInfo?.nickName.isNullOrEmpty()) {//姓名
                planPosition = 0
            } else if (it.baseInfo?.gender == 0) {//性别
                planPosition = 1
            } else if (it.baseInfo?.birthday.isNullOrEmpty()) {//生日
                planPosition = 2
            } else if (it.baseInfo?.addressCode.isNullOrEmpty()) {//现居地
                planPosition = 3
            } else if ((it.baseInfo?.degree ?: 0) < 10) {//学历
                planPosition = 4
            }else if (it.baseInfo?.loveGoal == 0) {//恋爱目标
                planPosition = 5
            } else if (it.profileTagList.isEmpty()) {//个性标签
                planPosition = 6
            }  else if (it.baseInfo?.height == 0) {//身高
                planPosition = 7
            } else if (it.baseInfo?.hukouCode.isNullOrEmpty()) {//户口
                planPosition = 8
            } else if (it.baseInfo?.hometownCode.isNullOrEmpty()) {//家乡
                planPosition = 9
            } else if (it.baseInfo?.industryCode.isNullOrEmpty()) {//行业、职业
                planPosition = 10
            } else if (it.baseInfo?.companyName.isNullOrEmpty()) {//公司
                planPosition = 12
            } else if (it.baseInfo?.carHold == 0 || it.baseInfo?.houseHold == 0) {//资产
                planPosition = 13
            } else if (it.interestTagList.isEmpty()) {//兴趣标签
                planPosition = 14
            } else if (!(it.storyList?.isNotEmpty() == true)) {//生活照
                planPosition = 15
            } else {//简介
                planPosition = 16
            }
        }
        initPosition = planPosition
        mViewModel.targetStartFragmentId = getPlanFragmentList()[planPosition]
    }

    private fun planCFirstPage() {
        planPosition = 0
        initPosition = planPosition
        mViewModel.targetStartFragmentId = getPlanFragmentList()[planPosition]
    }
}


