package com.kanzhun.marry.matching.adpter.home.recommendUser.bigAvatar

import android.content.Context
import androidx.core.view.isVisible
import androidx.lifecycle.lifecycleScope
import com.kanzhun.common.kotlin.ext.visible
import com.kanzhun.common.util.LText
import com.kanzhun.common.util.toast.OToast
import com.kanzhun.foundation.kernel.account.AccountHelper
import com.kanzhun.foundation.utils.point.reportPoint
import com.kanzhun.foundation.utils.ui.getComponentActivity
import com.kanzhun.foundation.views.SendLikeButton
import com.kanzhun.marry.matching.adpter.home.AutoUpdateHighQualityUnlockCountdownUseCase
import com.kanzhun.marry.matching.api.model.RecommendUser
import com.kanzhun.marry.matching.databinding.MatchingRecommendPagerItem2Binding
import com.kanzhun.marry.matching.module.highQualityUser.viewModel.HighQualityUserCountdown
import org.koin.core.component.KoinComponent
import org.koin.core.component.inject
import org.koin.core.qualifier.named

class MatchingRecommendUserHighQualityForInformalUserViewHolder(binding: MatchingRecommendPagerItem2Binding) :
    MatchingRecommendUserHighQualityViewHolder(binding), KoinComponent {

    private val autoUpdateHighQualityUnlockCountdownUseCase = AutoUpdateHighQualityUnlockCountdownUseCase()
    private val unlockHighQualityCountdown: HighQualityUserCountdown by inject(named(HighQualityUserCountdown.UNLOCK))
    private val expireHighQualityCountdown: HighQualityUserCountdown by inject(named(HighQualityUserCountdown.EXPIRE))

    override fun MatchingRecommendPagerItem2Binding.setupUnlockCountdown(data: RecommendUser) {
        highQualityUnlockContainer.visible()
        highQualityTag.apply {
            typeface = LText.getBoldTypeface(mBinding.root.context)
            includeFontPadding = false
        }
        unlockBtn.text = if (data.hasWellSelectedCardBeenUnlocked) "解锁成功" else "去解锁"
        unlockArrow.isVisible = !data.hasWellSelectedCardBeenUnlocked
        autoUpdateHighQualityUnlockCountdownUseCase(
            unlockCountdown,
            if (data.hasWellSelectedCardBeenUnlocked) {
                expireHighQualityCountdown.countdownFlow
            } else {
                unlockHighQualityCountdown.countdownFlow
            },
            root.context.getComponentActivity()?.lifecycleScope
        )
    }

    override fun SendLikeButton.customize(data: RecommendUser) {
        interceptClick = {
            reportClick(data, "1")
            if (data.overUnlockDeadlineAndNotUnlock) {
                onClick(mBinding.root.context, data)
                true
            } else {
                false
            }
        }
    }

    override fun onLikeNotificationClick(data: RecommendUser) {
        super.onLikeNotificationClick(data)
        reportClick(data, "3")
    }

    override fun dealRootClick(context: Context, data: RecommendUser) {
        super.dealRootClick(context, data)
        reportClick(data, "2")
    }

    /**
     * @param position: 1-点击喜欢按钮 2-点击其他位置 3-点击“喜欢我”标签
     */
    private fun reportClick(data: RecommendUser, position: String) {
        val countdownDeadlineMills = (if (data.hasWellSelectedCardBeenUnlocked) unlockHighQualityCountdown else expireHighQualityCountdown).deadlineMills
        val remainingTimeMills = System.currentTimeMillis() - countdownDeadlineMills
        reportPoint("F1-selected-rcd-user-click") {
            actionp3 = position
            actionp7 = if (data.hasWellSelectedCardBeenUnlocked) "2" else "1"
            actionp8 = remainingTimeMills.toString()
            peer_id = data.baseInfo?.encUserId
            status = AccountHelper.getInstance().phase.toString()
        }
    }

    override fun onClick(context: Context, data: RecommendUser) {
        if (data.overUnlockDeadlineAndNotUnlock) {
            OToast.show(context, "当前精选嘉宾已失效")
        } else {
            super.onClick(context, data)
        }
    }

    val RecommendUser.overUnlockDeadlineAndNotUnlock
        get() = System.currentTimeMillis() > unlockHighQualityCountdown.deadlineMills && !hasWellSelectedCardBeenUnlocked
}